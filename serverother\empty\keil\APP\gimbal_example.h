#ifndef _GIMBAL_EXAMPLE_H_
#define _GIMBAL_EXAMPLE_H_

#include "mydefine.h"

/**
 * @file gimbal_example.h
 * @brief 云台开环控制使用示例头文件
 */

// 示例函数声明
//void example_basic_angle_control(void);        // 基础角度控制示例
//void example_pixel_coordinate_control(void);   // 像素坐标控制示例
//void example_scan_pattern(void);               // 扫描模式示例
//void example_circular_trajectory(void);        // 圆形轨迹示例
//void example_config_adjustment(void);          // 配置参数调整示例
//void example_relative_movement(void);          // 相对移动示例

// 标定相关函数
//void gimbal_center_calibration(int current_pixel_x, int current_pixel_y); // 中心点标定
//void gimbal_key_calibration(uint8_t key_down, int current_x, int current_y); // 按键触发标定
//void gimbal_calibration_demo(void);           // 标定演示

// 主要接口函数
//void gimbal_run_examples(void);                // 运行所有示例
//void gimbal_quick_test(void);                  // 快速测试

#endif
