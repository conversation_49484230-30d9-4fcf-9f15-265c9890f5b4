#include "Task_func.h"
#include "motor_app.h"
#include "sine_wave.h"

uint8_t control_mode=1;
uint8_t open=0;


float target_xy[2]={0,0};


void pixel_debug_task(void)
{
	switch(state_data)
	{
		case 0:
			if(gimbal_check_coordinate_reached(0.09f,0.12f,0.05f,0.05f))
			{
				uart_printf(UART_0_INST, "运动完成1\r\n");
				state_data=1;
				state_up();
			}
			break;
		case 1:
//			if(gimbal_check_coordinate_reached(-0.09f, -0.12f,0.01f,0.01f))
//			{
//				uart_printf(UART_0_INST, "运动完成2\r\n");
//				state_data=2;
//				state_up();
//				
//			}
			break;
		case 2:
				if(gimbal_check_coordinate_reached(0.09f, 0.12f,0.01f,0.01f))
			{
				uart_printf(UART_0_INST, "运动完成3\r\n");
				state_data=3;
				state_up();
				
			}
		case 3:
				if(gimbal_check_coordinate_reached(0.09f, -0.12f,0.01f,0.01f))
			{
				uart_printf(UART_0_INST, "运动完成4\r\n");
				state_data=4;
				state_up();
			}
			break;
			break;
	}
}

void contorol_Task(void)
{
	if(open)
	{
			if(control_mode==0)
			{
				app_pid_calc();
			}
			else if(control_mode==1)  // 统一运动模式 (三角形/正弦波)
			{
				//pixel_debug_task();
				//unified_motion_task();
			}

	}

}
uint8_t state_data=0;

void state_up(void)
{
	switch(state_data)
	{
		case 0:
			//open=1;
			gimbal_move_to_target(0.09f, 0.13f, 30); 
			delay_ms(1800);
			gimbal_move_to_target(-0.10f,0.13f, 30); //y -  上 x 右 -
		  delay_ms(2000);
		  gimbal_move_to_target(-0.10f,-0.14f, 30); 
			delay_ms(2000);
  		gimbal_move_to_target(0.09f, -0.14f, 30); 
//			delay_ms(2000);
//			gimbal_move_to_target(0.10f, 0.13f, 30); 
			break;
		case 1:
			gimbal_move_to_target(-0.09f, -0.12f, 30); 
			break;
		case 2:
			gimbal_move_to_target(0.09f, -0.12f, 30); 
			break;
		case 3:
			gimbal_move_to_target(0.09f, 0.12f, 30); 
			break;
		case 4:
			gimbal_move_to_target(0.09f, -0.12f, 30); 
			break;
	}
}