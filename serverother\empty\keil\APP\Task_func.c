#include "Task_func.h"
#include "motor_app.h"
#include "sine_wave.h"

uint8_t control_mode=1;
uint8_t open=0;

// 添加新的控制模式定义（在头文件中）
#define CONTROL_MODE_PID           0
#define CONTROL_MODE_UNIFIED       1
#define CONTROL_MODE_PIXEL_DEBUG   2  // 新增像素调试模式

// 像素调试状态枚举
typedef enum {
    PIXEL_DEBUG_IDLE = 0,
    PIXEL_DEBUG_TO_CENTER,
    PIXEL_DEBUG_WAIT_CENTER,
    PIXEL_DEBUG_TO_LEFT_TOP,
    PIXEL_DEBUG_WAIT_LEFT_TOP,
    PIXEL_DEBUG_TO_CENTER_BACK,
    PIXEL_DEBUG_WAIT_CENTER_BACK,
    PIXEL_DEBUG_COMPLETE
} pixel_debug_state_t;

// 全局变量
static pixel_debug_state_t pixel_debug_state = PIXEL_DEBUG_IDLE;
static uint8_t pixel_debug_started = 0;

// 非阻塞等待函数
uint8_t check_position_reached(float target_x, float target_y, float tolerance) {
    float diff_x = fabs(Motor_Cur_Pos[0] - target_x);
    float diff_y = fabs(Motor_Cur_Pos[1] - target_y);
    return (diff_x <= tolerance && diff_y <= tolerance);
}

void pixel_debug_task(void) {
    static float center_x, center_y, left_top_x, left_top_y;
    
    switch(pixel_debug_state) {
        case PIXEL_DEBUG_IDLE:
            if(!pixel_debug_started) 
            {
                // 初始化调试参数
                center_x = pixel_to_angle_x(320);
                center_y = pixel_to_angle_y(240);
                left_top_x = pixel_to_angle_x(160);
                left_top_y = pixel_to_angle_y(120);
                
                uart_printf(UART_0_INST, "=== 像素调试开始 ===\r\n");
                uart_printf(UART_0_INST, "当前云台位置: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
                uart_printf(UART_0_INST, "目标中心(320,240) -> 角度(%.2f°,%.2f°)\r\n", center_x, center_y);
                uart_printf(UART_0_INST, "目标左上(160,120) -> 角度(%.2f°,%.2f°)\r\n", left_top_x, left_top_y);
                uart_printf(UART_0_INST, "角度容差: ±0.5°\r\n");
                
                pixel_debug_started = 1;
                pixel_debug_state = PIXEL_DEBUG_TO_CENTER;
            }
            break;
            
        case PIXEL_DEBUG_TO_CENTER:
            uart_printf(UART_0_INST, "\n--- 步骤1：移动到中心 ---\r\n");
            uart_printf(UART_0_INST, "当前位置: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
            uart_printf(UART_0_INST, "目标位置: %.2f°, %.2f°\r\n", center_x, center_y);
            uart_printf(UART_0_INST, "开始移动到中心(320,240)...\r\n");
            // 使用精准的角度控制移动到中心
            motor_move_angle(center_x, center_y, 40, 0);
            pixel_debug_state = PIXEL_DEBUG_WAIT_CENTER;
            break;
            
        case PIXEL_DEBUG_WAIT_CENTER:
            if(check_position_reached(center_x, center_y, 0.5f)) {
                uart_printf(UART_0_INST, "✓ 成功到达中心: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
                float error_x = fabs(Motor_Cur_Pos[0] - center_x);
                float error_y = fabs(Motor_Cur_Pos[1] - center_y);
                uart_printf(UART_0_INST, "位置误差: X=%.3f°, Y=%.3f°\r\n", error_x, error_y);
                pixel_debug_state = PIXEL_DEBUG_TO_LEFT_TOP;
            } else {
                // 显示当前进度
                static uint32_t wait_counter = 0;
                wait_counter++;
                if(wait_counter % 100 == 0) { // 每100次循环显示一次
                    uart_printf(UART_0_INST, "等待到达中心... 当前: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
                }
            }
            break;
            
        case PIXEL_DEBUG_TO_LEFT_TOP:
            uart_printf(UART_0_INST, "\n--- 步骤2：移动到左上角 ---\r\n");
            uart_printf(UART_0_INST, "当前位置: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
            uart_printf(UART_0_INST, "目标位置: %.2f°, %.2f°\r\n", left_top_x, left_top_y);
            uart_printf(UART_0_INST, "开始移动到左上角(160,120)...\r\n");
            // 使用精准的角度控制移动到左上角
            motor_move_angle(left_top_x, left_top_y, 40, 0);
            pixel_debug_state = PIXEL_DEBUG_WAIT_LEFT_TOP;
            break;
            
        case PIXEL_DEBUG_WAIT_LEFT_TOP:
            if(check_position_reached(left_top_x, left_top_y, 0.5f)) {
                uart_printf(UART_0_INST, "✓ 成功到达左上角: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
                float error_x = fabs(Motor_Cur_Pos[0] - left_top_x);
                float error_y = fabs(Motor_Cur_Pos[1] - left_top_y);
                uart_printf(UART_0_INST, "位置误差: X=%.3f°, Y=%.3f°\r\n", error_x, error_y);
                pixel_debug_state = PIXEL_DEBUG_TO_CENTER_BACK;
            } else {
                // 显示当前进度
                static uint32_t wait_counter2 = 0;
                wait_counter2++;
                if(wait_counter2 % 100 == 0) { // 每100次循环显示一次
                    uart_printf(UART_0_INST, "等待到达左上角... 当前: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
                }
            }
            break;
            
        case PIXEL_DEBUG_TO_CENTER_BACK:
            uart_printf(UART_0_INST, "\n--- 步骤3：回到中心 ---\r\n");
            uart_printf(UART_0_INST, "当前位置: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
            uart_printf(UART_0_INST, "目标位置: %.2f°, %.2f°\r\n", center_x, center_y);
            uart_printf(UART_0_INST, "使用精准角度控制回到中心...\r\n");
            // 使用精准的角度控制回到中心
            motor_move_angle(center_x, center_y, 40, 0);
            pixel_debug_state = PIXEL_DEBUG_WAIT_CENTER_BACK;
            break;
            
        case PIXEL_DEBUG_WAIT_CENTER_BACK:
            if(check_position_reached(center_x, center_y, 0.5f)) {
                uart_printf(UART_0_INST, "✓ 成功回到中心: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
                float error_x = fabs(Motor_Cur_Pos[0] - center_x);
                float error_y = fabs(Motor_Cur_Pos[1] - center_y);
                uart_printf(UART_0_INST, "最终位置误差: X=%.3f°, Y=%.3f°\r\n", error_x, error_y);
                pixel_debug_state = PIXEL_DEBUG_COMPLETE;
            } else {
                // 显示当前进度
                static uint32_t wait_counter3 = 0;
                wait_counter3++;
                if(wait_counter3 % 100 == 0) { // 每100次循环显示一次
                    uart_printf(UART_0_INST, "等待回到中心... 当前: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
                }
            }
            break;
            
        case PIXEL_DEBUG_COMPLETE:
            uart_printf(UART_0_INST, "\n=== 像素调试完成 ===\r\n");
            uart_printf(UART_0_INST, "最终位置: %.2f°, %.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
            uart_printf(UART_0_INST, "调试总结:\r\n");
            uart_printf(UART_0_INST, "- 中心位置: (%.2f°, %.2f°)\r\n", center_x, center_y);
            uart_printf(UART_0_INST, "- 左上位置: (%.2f°, %.2f°)\r\n", left_top_x, left_top_y);
            uart_printf(UART_0_INST, "- 使用精准角度控制，运动完成\r\n");
            
            pixel_debug_state = PIXEL_DEBUG_IDLE;
            pixel_debug_started = 0;
            // 可以选择切换回其他控制模式
            // control_mode = 0; // 切换回PID模式
            break;
    }
}


void contorol_Task(void)
{
	if(open)
	{
			if(control_mode==0)
			{
				app_pid_calc();
			}
			else if(control_mode==1)  // 统一运动模式 (三角形/正弦波)
			{
				pixel_debug_task();
				//unified_motion_task();
			}

	}

}
uint8_t state_data=2;

void state_up(void)
{
	switch(state_data)
	{
		case 0:
			
			break;
		case 1:
			pid_target(xy_z[0][0],xy_z[0][1]);
			open=1;
			break;
		case 2:
			pid_target(xy[0][0],xy[0][1]);
			open=1;
			break;
	}
}