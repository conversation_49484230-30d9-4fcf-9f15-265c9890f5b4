#ifndef _DMA_APP_H_
#define _DMA_APP_H_


#include "mydefine.h"
#include <stdarg.h>  // 用于可变参数函数

typedef struct
{
    uint8_t slave_id;
    float roll;    // 横滚角，单位度
    float pitch;   // 俯仰角，单位度
    float yaw;     // 偏航角，单位度
    uint16_t crc16; // CRC校验
} FrameData_t;

extern uint8_t angle_flag;
uint8_t speed_cal_read(void);
extern FrameData_t currentFrame;

// 串口1超时解析相关定义
#define UART1_PACKET_SIZE  (64)  // 串口1数据包大小
#define UART1_TIMEOUT_MS   (3)   // 串口1超时时间3ms（优化：减少触发延迟）

// 串口2超时解析相关定义
#define UART2_PACKET_SIZE  (64)  // 串口2数据包大小
#define UART2_TIMEOUT_MS   (3)  // 串口2超时时间10ms

void chassis_distance_speed(float distance, float target_omega);
extern double distance;



// 串口1超时解析结构体
typedef struct {
    volatile uint8_t buffer[UART1_PACKET_SIZE];
    volatile uint16_t rxIndex;     // 接收索引
    volatile bool dataReady;       // 数据就绪标志
    volatile uint32_t lastRxTime;  // 最后接收时间
    volatile bool timeoutFlag;     // 超时标志
} UART1_Handle_t;

// 串口2超时解析结构体
typedef struct {
    volatile uint8_t buffer[UART2_PACKET_SIZE];
    volatile uint16_t rxIndex;     // 接收索引
    volatile bool dataReady;       // 数据就绪标志
    volatile uint32_t lastRxTime;  // 最后接收时间
    volatile bool timeoutFlag;     // 超时标志
} UART2_Handle_t;

// 运动控制结构体
typedef struct {
    float target_v;               // 目标线速度
    float target_omega;           // 目标角速度
    uint32_t start_time;          // 运动开始时间
    uint32_t duration_ms;         // 运动持续时间(毫秒)
    bool is_active;               // 运动是否激活
    bool auto_stop;               // 是否自动停止
} MotionControl_t;

void UART_DMA_Init(void);
void DMA_REST(void);

// 串口1超时解析函数
void UART1_Timeout_Init(void);     // 串口1超时初始化
void UART1_CheckTimeout(void);     // 串口1超时检查
void UART1_ProcessData(void);      // 串口1数据处理

// 串口2超时解析函数
void UART2_Timeout_Init(void);     // 串口2超时初始化
void UART2_CheckTimeout(void);     // 串口2超时检查
void UART2_ProcessData(void);      // 串口2数据处理

// 测试函数
void Test_UART1_MotorData(void);   // UART1电机数据解析测试

void ProcessReceivedData(void);

// 基础串口函数
void uart_send_char(UART_Regs *uart, char ch);
void uart_send_string(UART_Regs *uart, const char* str);
void uart_send_data(UART_Regs *uart, const unsigned char* data, int length);

// 格式化输出函数
int uart_printf(UART_Regs *uart, const char *format, ...);        // 统一使用uart_printf

// 辅助转换函数
int my_strlen(const char* str);
void int_to_string(int num, char* str);
void uint_to_hex_string(unsigned int num, char* str, int uppercase);
void float_to_string(float num, char* str, int precision);

// 调试函数
void send_hex_byte(uint8_t byte);

// 命令解析函数
void uart2_parse_command(char *cmd);

// ==================== 视觉数据包解析功能接口 ====================

/**
 * @brief 视觉数据包类型枚举
 */
typedef enum {
    VISION_CMD_RECTANGLE = 0x01,    // 矩形检测数据包
    VISION_CMD_RED_TARGET = 0x02    // 红色目标检测数据包
} VisionPacketType_t;

/**
 * @brief 视觉数据包解析状态枚举
 */
typedef enum {
    VISION_PARSE_OK = 0,           // 解析成功
    VISION_PARSE_INVALID_HEADER,   // 无效帧头
    VISION_PARSE_INVALID_LENGTH,   // 无效数据长度
    VISION_PARSE_CHECKSUM_ERROR,   // 校验和错误
    VISION_PARSE_UNKNOWN_CMD       // 未知命令类型
} VisionParseResult_t;

/**
 * @brief 红色目标检测结果结构体
 */
typedef struct {
    uint8_t x;    // X坐标 (0-255)
    uint8_t y;    // Y坐标 (0-255)
} RedTargetData_t;

/**
 * @brief 矩形检测结果结构体
 */
typedef struct {
    uint8_t x1, y1;    // 第一个关键点坐标
    uint8_t x2, y2;    // 第二个关键点坐标
    uint8_t x3, y3;    // 第三个关键点坐标
    uint8_t x4, y4;    // 第四个关键点坐标
} RectangleData_t;

/**
 * @brief 视觉数据包解析结果结构体
 */
typedef struct {
    VisionPacketType_t packet_type;    // 数据包类型
    union {
        RedTargetData_t red_target;    // 红色目标数据
        RectangleData_t rectangle;     // 矩形数据
    } data;
} VisionPacketData_t;

// 视觉数据包解析函数接口
VisionParseResult_t parse_vision_packet(const uint8_t* buffer, uint16_t buffer_len, VisionPacketData_t* result);
bool get_red_target_coordinates(const VisionPacketData_t* packet_data, uint8_t* x, uint8_t* y);
bool get_rectangle_coordinates(const VisionPacketData_t* packet_data,
                              uint8_t* x1, uint8_t* y1, uint8_t* x2, uint8_t* y2,
                              uint8_t* x3, uint8_t* y3, uint8_t* x4, uint8_t* y4);
const char* get_vision_parse_error_string(VisionParseResult_t result);
extern float Motor_Cur_Pos[2];

// 使用示例函数
void vision_packet_parse_example(const uint8_t* uart_buffer, uint16_t buffer_len);

// 测试函数
void test_vision_packet_parser(void);

// ==================== 串口数据包解析功能接口 ====================


void uart_packet_parse_example(const uint8_t* buffer, uint16_t buffer_len);

// 两点坐标数据处理函数声明
void handle_two_points_data(uint16_t point1_x, uint16_t point1_y, uint16_t point2_x, uint16_t point2_y);

#endif


