#include "motor_mid.h"

unsigned char RE[6]={0x01,0xF3,0xAB,0x01,0x00,0x6B};
unsigned char SE[13]={0x01,0xFD,0x01,0x00,0x3C,0x00,0x00,0x00,0x0C,0x80,0x00,0x01,0x6B};
unsigned char SE1[8]={0x01,0xF6,0x01,0x00,0x3C,0x00,0x01,0x6B};
unsigned char TB[4]={0x00,0xFF,0x66,0x6B};
unsigned char STP[5]={0x00,0xFE,0x98,0x00,0x6B};
unsigned char READ[3]={0x00,0x36,0x6B};
unsigned char READ_SPEED[3]={0x00,0x35,0x6B};  // 电机速度读取命令
unsigned char CLEAR[4]={0x01,0x0A,0x6D,0x6B};  // 电机清零命令
unsigned char cmd[16] = {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,};

#define		ABS(x)		((x) > 0 ? (x) : -(x)) 




void motor_init()
{
	RE[0]=0x01;
	uart_send_data(UART_1_INST,RE,6);
	delay_ms(10);
	RE[0]=0x02;
	uart_send_data(UART_1_INST,RE,6);
	delay_ms(10);
	motor_stop();
}
void motor_distance(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint32_t pulse,uint8_t mode,uint8_t sync)
{
	SE[0]=id;
	SE[2]=turn;
	SE[3]=speed >> 8;
	SE[4]=speed;
	SE[5]=accel;
	SE[6]=pulse>>24;
	SE[7]=pulse>>16;
	SE[8]=pulse>>8;
	SE[9]=pulse;
	SE[10]=mode;
	SE[11]=sync;
	uart_send_data(UART_1_INST,SE,13);
	delay_ms(10);
}


void motor_speed(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint8_t sync)
{
	SE1[0]=id;
	SE1[2]=turn;
	SE1[3]=speed >> 8;
	SE1[4]=speed;
	SE1[5]=accel;
	SE1[6]=sync;
	uart_send_data(UART_1_INST,SE1,8);
	delay_ms(10);
}




void motor_readdistance(uint8_t id)
{

	READ[0]=id;
	uart_send_data(UART_1_INST,READ,3);
	delay_ms(10);
}

/**
 * @brief 电机速度读取函数
 *
 * 发送速度读取命令：地址 + 0x35 + 校验字节
 * - 地址: 电机ID (1或2)
 * - 0x35: 速度读取功能码
 * - 0x6B: 校验字节
 *
 * 返回格式：
 * - 正确返回：地址 + 0x35 + 符号 + 转速数据 + 校验字节 (6字节)
 * - 错误返回：地址 + 0x00 + 校验字节 (3字节)
 *
 * @param id 电机ID (1或2)
 */
void motor_readspeed(uint8_t id)
{
	READ_SPEED[0] = id;
	uart_send_data(UART_1_INST, READ_SPEED, 3);
}
void motor_stop()
{
	uart_send_data(UART_1_INST,STP,5);
	delay_ms(10);
}

/**
 * @brief 电机位置清零函数
 *
 * 发送清零命令：01 0A 6D 6B
 * - 0x01: 电机ID (可修改为指定电机)
 * - 0x0A: 清零功能码
 * - 0x6D: 参数
 * - 0x6B: 校验字节
 *
 * @param id 电机ID (1或2)
 */
void motor_clear_position(uint8_t id)
{
	CLEAR[0] = id;  // 设置电机ID
	uart_send_data(UART_1_INST, CLEAR, 4);
	delay_ms(10);
}

/**
 * @brief 所有电机位置清零函数
 *
 * 对所有电机(ID 1和2)执行位置清零操作
 */
void motor_clear_all_position(void)
{
	// 清零电机1
	motor_clear_position(1);

	// 清零电机2
	motor_clear_position(2);
}
void motor_sync()
{
	uart_send_data(UART_1_INST,TB,4);
	delay_ms(10);
}


#define MOTOR_MAX_SPEED 50 

void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* 限制百分比范围 */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* 设置X轴方向 */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW方向 */
    }
    else
    {
        x_dir = 1;              /* CCW方向 */
        x_percent = -x_percent; /* 取绝对值 */
    }

    /* 设置Y轴方向 */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW方向 */
    }
    else
    {
        y_dir = 1;              /* CCW方向 */
        y_percent = -y_percent; /* 取绝对值 */
    }

    /* 计算实际速度值(百分比转换为RPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

		motor_speed(0x01,x_dir,x_speed,0,1);
		motor_speed(0x02,y_dir,y_speed,0,1);
		
		motor_sync();
		
}


void dis_read(void)
{
	static uint8_t read_count = 0 ;
	if(++read_count==3)read_count=1;
	motor_readdistance(read_count%3);
}


// 云台角度到像素转换配置参数 (根据实际相机参数设置)
#define GIMBAL_FOV_HORIZONTAL_DEG    17.5f    // 水平视场角(实际测量值)
#define GIMBAL_FOV_VERTICAL_DEG      11.20f   // 垂直视场角(实际测量值)
#define GIMBAL_IMAGE_WIDTH           640      // 图像宽度
#define GIMBAL_IMAGE_HEIGHT          480      // 图像高度
#define GIMBAL_IMAGE_CENTER_X        320      // 图像中心X
#define GIMBAL_IMAGE_CENTER_Y        240      // 图像中心Y
#define GIMBAL_MAX_PAN_DEG           30.0f    // 最大水平角度范围(建议为FOV的1.5-2倍)
#define GIMBAL_MAX_TILT_DEG          20.0f    // 最大俯仰角度范围(建议为FOV的1.5-2倍)

// 角度限制函数
static float constrain_angle(float angle, float max_angle) {
    if (angle > max_angle) return max_angle;
    if (angle < -max_angle) return -max_angle;
    return angle;
}

// 水平角度转像素X坐标
int angle_to_pixel_x(float angle) {
    // 限制角度范围
    angle = constrain_angle(angle, GIMBAL_FOV_HORIZONTAL_DEG / 2.0f);

    // 线性映射：角度 -> 像素
    // 角度范围：[-FOV/2, +FOV/2] -> 像素范围：[0, IMAGE_WIDTH]
    float normalized = angle / (GIMBAL_FOV_HORIZONTAL_DEG / 2.0f); // [-1, +1]
    int pixel_x = GIMBAL_IMAGE_CENTER_X + (int)(normalized * (GIMBAL_IMAGE_WIDTH / 2));

    // 限制像素范围
    if (pixel_x < 0) pixel_x = 0;
    if (pixel_x >= GIMBAL_IMAGE_WIDTH) pixel_x = GIMBAL_IMAGE_WIDTH - 1;

    return pixel_x;
}

// 垂直角度转像素Y坐标
int angle_to_pixel_y(float angle) {
    // 限制角度范围
    angle = constrain_angle(angle, GIMBAL_FOV_VERTICAL_DEG / 2.0f);

    // 线性映射：角度 -> 像素 (注意Y轴方向：正角度对应较小的像素值)
    float normalized = -angle / (GIMBAL_FOV_VERTICAL_DEG / 2.0f); // 反转Y轴
    int pixel_y = GIMBAL_IMAGE_CENTER_Y + (int)(normalized * (GIMBAL_IMAGE_HEIGHT / 2));

    // 限制像素范围
    if (pixel_y < 0) pixel_y = 0;
    if (pixel_y >= GIMBAL_IMAGE_HEIGHT) pixel_y = GIMBAL_IMAGE_HEIGHT - 1;

    return pixel_y;
}

// 像素X坐标转水平角度（扩展范围版本）
float pixel_to_angle_x(int pixel_x) {
    // 不限制像素范围，允许超出图像边界的转换
    // 线性映射：像素 -> 角度
    float normalized = (float)(pixel_x - GIMBAL_IMAGE_CENTER_X) / (GIMBAL_IMAGE_WIDTH / 2);
    float angle = normalized * (GIMBAL_FOV_HORIZONTAL_DEG / 2.0f);

    // 只在最后限制云台物理角度范围
    return constrain_angle(angle, GIMBAL_MAX_PAN_DEG);
}

// 像素Y坐标转垂直角度（扩展范围版本）
float pixel_to_angle_y(int pixel_y) {
    // 不限制像素范围，允许超出图像边界的转换
    // 线性映射：像素 -> 角度 (注意Y轴方向反转)
    float normalized = (float)(pixel_y - GIMBAL_IMAGE_CENTER_Y) / (GIMBAL_IMAGE_HEIGHT / 2);
    float angle = -normalized * (GIMBAL_FOV_VERTICAL_DEG / 2.0f); // 反转Y轴

    // 只在最后限制云台物理角度范围
    return constrain_angle(angle, GIMBAL_MAX_TILT_DEG);
}

// 移动到指定像素位置(开环控制)
void move_to_pixel(int pixel_x, int pixel_y, uint8_t speed) {
    // 像素坐标转换为云台角度
    float angle_x = pixel_to_angle_x(pixel_x);
    float angle_y = pixel_to_angle_y(pixel_y);

    // 使用角度控制云台运动
    motor_move_angle(angle_x, angle_y, speed, 50); // 默认加速度50
}

//float time=0.0f;

void motor_move_angle(float angle_x, float angle_y, uint8_t speed_percent, uint8_t accel)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse, y_pulse;
    uint16_t actual_speed;

    // 限制速度百分比范围
    if(speed_percent > 100) speed_percent = 100;
    if(speed_percent < 1) speed_percent = 1;

    // 计算实际速度值(百分比转换为RPM，与Motor_Set_Speed保持一致)
    actual_speed = (uint16_t)((speed_percent * MOTOR_MAX_SPEED) / 100);

    // 确定X轴方向和脉冲数
    if(angle_x >= 0) {
        x_dir = 0;  // CW方向 (顺时针)
    } else {
        x_dir = 1;  // CCW方向 (逆时针)
        angle_x = -angle_x;  // 取绝对值
    }

    // 确定Y轴方向和脉冲数
    if(angle_y >= 0) {
        y_dir = 0;  // CW方向 (顺时针)
    } else {
        y_dir = 1;  // CCW方向 (逆时针)
        angle_y = -angle_y;  // 取绝对值
    }

    // 角度转脉冲数计算：使用控制分辨率
    // 控制分辨率：MOTOR_CONTROL_PULSES_PER_REV脉冲 = 360度 (16细分)
    // 读取分辨率：MOTOR_ENCODER_PULSES_PER_REV脉冲 = 360度 (编码器反馈)
    // 脉冲数 = 角度 * (MOTOR_CONTROL_PULSES_PER_REV / 360)
    x_pulse = (uint32_t)(angle_x * 3200 / 360.0f + 0.5f);  // +0.5f用于四舍五入
    y_pulse = (uint32_t)(angle_y * 3200 / 360.0f + 0.5f);  // +0.5f用于四舍五入
//		float time_T,y_speed,x_speed=0.0f;
//		
		float time_T;
		uint16_t x_speed, y_speed;

		if(x_pulse == 0 && y_pulse == 0) {
			// 两轴都不运动
			x_speed = actual_speed;
			y_speed = actual_speed;
		} else if(x_pulse == 0) {
			// 只有Y轴运动
			x_speed = 1;  // 最小速度
			y_speed = actual_speed;
		} else if(y_pulse == 0) {
			// 只有X轴运动
			x_speed = actual_speed;
			y_speed = 1;  // 最小速度
		} else if(x_pulse > y_pulse) {
			// X轴距离大，X轴用最大速度，Y轴按比例降速
			time_T = (float)x_pulse / actual_speed;
			x_speed = actual_speed;
			y_speed = (uint16_t)((float)y_pulse / time_T + 0.5f);  // 四舍五入
		} else if(y_pulse > x_pulse) {
			// Y轴距离大，Y轴用最大速度，X轴按比例降速
			time_T = (float)y_pulse / actual_speed;
			y_speed = actual_speed;
			x_speed = (uint16_t)((float)x_pulse / time_T + 0.5f);  // 四舍五入
		} else {
			// 距离相等，使用相同速度
			x_speed = actual_speed;
			y_speed = actual_speed;
		}

		// 确保速度不为0
		if(x_pulse > 0 && x_speed < 1) x_speed = 1;
		if(y_pulse > 0 && y_speed < 1) y_speed = 1;

    // 发送X轴电机控制命令 (ID=1, 相对位置模式, 启用同步)
    motor_distance(0x01, x_dir, x_speed, accel, x_pulse, 0, 1);

    // 发送Y轴电机控制命令 (ID=2, 相对位置模式, 启用同步)
    motor_distance(0x02, y_dir, y_speed, accel, y_pulse, 0, 1);
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
    // 发送同步命令，两个电机同时开始运动
    motor_sync();
}



// 全局配置变量
static gimbal_config_t gimbal_config = {
    .fov_horizontal_deg = GIMBAL_FOV_HORIZONTAL_DEG,
    .fov_vertical_deg = GIMBAL_FOV_VERTICAL_DEG,
    .image_width = GIMBAL_IMAGE_WIDTH,
    .image_height = GIMBAL_IMAGE_HEIGHT,
    .image_center_x = GIMBAL_IMAGE_CENTER_X,
    .image_center_y = GIMBAL_IMAGE_CENTER_Y,
    .max_pan_deg = GIMBAL_MAX_PAN_DEG,
    .max_tilt_deg = GIMBAL_MAX_TILT_DEG
};

// 设置云台配置参数
void gimbal_set_config(float fov_h, float fov_v, int img_w, int img_h, float max_pan, float max_tilt) {
    gimbal_config.fov_horizontal_deg = fov_h;
    gimbal_config.fov_vertical_deg = fov_v;
    gimbal_config.image_width = img_w;
    gimbal_config.image_height = img_h;
    gimbal_config.image_center_x = img_w / 2;
    gimbal_config.image_center_y = img_h / 2;
    gimbal_config.max_pan_deg = max_pan;
    gimbal_config.max_tilt_deg = max_tilt;
}

// 获取当前云台配置
gimbal_config_t* gimbal_get_config(void) {
    return &gimbal_config;
}

// 设置图像中心点（用于标定）
void gimbal_set_center(int center_x, int center_y) {
    gimbal_config.image_center_x = center_x;
    gimbal_config.image_center_y = center_y;
    uart_printf(UART_0_INST, "图像中心已更新为: (%d,%d)\r\n", center_x, center_y);
}

// 云台移动到绝对角度位置
void gimbal_move_to_angle(float pan_deg, float tilt_deg, uint8_t speed) {
    motor_move_angle(pan_deg, tilt_deg, speed, 50);
}

// 云台相对角度移动
void gimbal_move_relative(float delta_pan, float delta_tilt, uint8_t speed) {
    // 注意：这里需要当前位置信息，暂时使用相对移动
    motor_move_angle(delta_pan, delta_tilt, speed, 50);
}

// 测试函数：打印角度和像素的对应关系
void gimbal_test_conversion(void) {
    float test_angles[] = {-30.0f, -15.0f, 0.0f, 15.0f, 30.0f};
    int test_pixels_x[] = {160, 240, 320, 400, 480};
    int test_pixels_y[] = {120, 180, 240, 300, 360};

    uart_printf(UART_0_INST, "=== 云台角度像素转换测试 ===\r\n");
    uart_printf(UART_0_INST, "配置: FOV_H=%.1f° FOV_V=%.1f° 分辨率=%dx%d\r\n",
                gimbal_config.fov_horizontal_deg, gimbal_config.fov_vertical_deg,
                gimbal_config.image_width, gimbal_config.image_height);

    uart_printf(UART_0_INST, "\n角度->像素转换:\r\n");
    for(int i = 0; i < 5; i++) {
        int px = angle_to_pixel_x(test_angles[i]);
        int py = angle_to_pixel_y(test_angles[i]);
        uart_printf(UART_0_INST, "角度(%.1f°,%.1f°) -> 像素(%d,%d)\r\n",
                    test_angles[i], test_angles[i], px, py);
    }

    uart_printf(UART_0_INST, "\n像素->角度转换:\r\n");
    for(int i = 0; i < 5; i++) {
        float ax = pixel_to_angle_x(test_pixels_x[i]);
        float ay = pixel_to_angle_y(test_pixels_y[i]);
        uart_printf(UART_0_INST, "像素(%d,%d) -> 角度(%.2f°,%.2f°)\r\n",
                    test_pixels_x[i], test_pixels_y[i], ax, ay);
    }
}


