#include "motor_mid.h"

unsigned char RE[6]={0x01,0xF3,0xAB,0x01,0x00,0x6B};
unsigned char SE[13]={0x01,0xFD,0x01,0x00,0x3C,0x00,0x00,0x00,0x0C,0x80,0x00,0x01,0x6B};
unsigned char SE1[8]={0x01,0xF6,0x01,0x00,0x3C,0x00,0x01,0x6B};
unsigned char TB[4]={0x00,0xFF,0x66,0x6B};
unsigned char STP[5]={0x00,0xFE,0x98,0x00,0x6B};
unsigned char READ[3]={0x00,0x36,0x6B};
unsigned char READ_SPEED[3]={0x00,0x35,0x6B};  // 电机速度读取命令
unsigned char CLEAR[4]={0x01,0x0A,0x6D,0x6B};  // 电机清零命令
unsigned char cmd[16] = {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,};

#define		ABS(x)		((x) > 0 ? (x) : -(x)) 




void motor_init()
{
	RE[0]=0x01;
	uart_send_data(UART_1_INST,RE,6);
	delay_ms(10);
	RE[0]=0x02;
	uart_send_data(UART_1_INST,RE,6);
	delay_ms(10);
	motor_stop();
}
void motor_distance(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint32_t pulse,uint8_t mode,uint8_t sync)
{
	SE[0]=id;
	SE[2]=turn;
	SE[3]=speed >> 8;
	SE[4]=speed;
	SE[5]=accel;
	SE[6]=pulse>>24;
	SE[7]=pulse>>16;
	SE[8]=pulse>>8;
	SE[9]=pulse;
	SE[10]=mode;
	SE[11]=sync;
	uart_send_data(UART_1_INST,SE,13);
	delay_ms(10);
}


void motor_speed(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint8_t sync)
{
	SE1[0]=id;
	SE1[2]=turn;
	SE1[3]=speed >> 8;
	SE1[4]=speed;
	SE1[5]=accel;
	SE1[6]=sync;
	uart_send_data(UART_1_INST,SE1,8);
	delay_ms(10);
}




void motor_readdistance(uint8_t id)
{

	READ[0]=id;
	uart_send_data(UART_1_INST,READ,3);
	delay_ms(10);
}

/**
 * @brief 电机速度读取函数
 *
 * 发送速度读取命令：地址 + 0x35 + 校验字节
 * - 地址: 电机ID (1或2)
 * - 0x35: 速度读取功能码
 * - 0x6B: 校验字节
 *
 * 返回格式：
 * - 正确返回：地址 + 0x35 + 符号 + 转速数据 + 校验字节 (6字节)
 * - 错误返回：地址 + 0x00 + 校验字节 (3字节)
 *
 * @param id 电机ID (1或2)
 */
void motor_readspeed(uint8_t id)
{
	READ_SPEED[0] = id;
	uart_send_data(UART_1_INST, READ_SPEED, 3);
}
void motor_stop()
{
	uart_send_data(UART_1_INST,STP,5);
	delay_ms(10);
}

/**
 * @brief 电机位置清零函数
 *
 * 发送清零命令：01 0A 6D 6B
 * - 0x01: 电机ID (可修改为指定电机)
 * - 0x0A: 清零功能码
 * - 0x6D: 参数
 * - 0x6B: 校验字节
 *
 * @param id 电机ID (1或2)
 */
void motor_clear_position(uint8_t id)
{
	CLEAR[0] = id;  // 设置电机ID
	uart_send_data(UART_1_INST, CLEAR, 4);
	delay_ms(10);
}

/**
 * @brief 所有电机位置清零函数
 *
 * 对所有电机(ID 1和2)执行位置清零操作
 */
void motor_clear_all_position(void)
{
	// 清零电机1
	motor_clear_position(1);

	// 清零电机2
	motor_clear_position(2);
}
void motor_sync()
{
	uart_send_data(UART_1_INST,TB,4);
	delay_ms(10);
}


#define MOTOR_MAX_SPEED 50 

void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* 限制百分比范围 */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* 设置X轴方向 */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW方向 */
    }
    else
    {
        x_dir = 1;              /* CCW方向 */
        x_percent = -x_percent; /* 取绝对值 */
    }

    /* 设置Y轴方向 */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW方向 */
    }
    else
    {
        y_dir = 1;              /* CCW方向 */
        y_percent = -y_percent; /* 取绝对值 */
    }

    /* 计算实际速度值(百分比转换为RPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

		motor_speed(0x01,x_dir,x_speed,0,1);
		motor_speed(0x02,y_dir,y_speed,0,1);
		
		motor_sync();
		
}


void dis_read(void)
{
	static uint8_t read_count = 0 ;
	if(++read_count==3)read_count=1;
	motor_readdistance(read_count%3);
}






void motor_move_angle(float angle_x, float angle_y, uint8_t speed_percent, uint8_t accel)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse, y_pulse;
    uint16_t base_speed, x_speed, y_speed;

    // 限制速度百分比范围
    if(speed_percent > 100) speed_percent = 100;
    if(speed_percent < 1) speed_percent = 1;

    // 计算基础速度值(百分比转换为RPM)
    base_speed = (uint16_t)((speed_percent * MOTOR_MAX_SPEED) / 100);

    // 确定X轴方向和脉冲数
    if(angle_x >= 0) {
        x_dir = 0;  // CW方向 (顺时针)
    } else {
        x_dir = 1;  // CCW方向 (逆时针)
        angle_x = -angle_x;  // 取绝对值
    }

    // 确定Y轴方向和脉冲数
    if(angle_y >= 0) {
        y_dir = 0;  // CW方向 (顺时针)
    } else {
        y_dir = 1;  // CCW方向 (逆时针)
        angle_y = -angle_y;  // 取绝对值
    }

    // 角度转脉冲数计算：使用控制分辨率
    // 脉冲数 = 角度 * (3200 / 360)
    x_pulse = (uint32_t)(angle_x * 3200 / 360.0f + 0.5f);  // +0.5f用于四舍五入
    y_pulse = (uint32_t)(angle_y * 3200 / 360.0f + 0.5f);  // +0.5f用于四舍五入

    // 速度比例分配：确保两轴同时到达目标点
    if(x_pulse == 0 && y_pulse == 0) {
        // 无运动，直接返回
        uart_printf(UART_0_INST, "无运动：两轴角度都为0\r\n");
        return;
    } else if(x_pulse == 0) {
        // 只有Y轴运动
        x_speed = 1;  // 最小速度，避免为0
        y_speed = base_speed;
        uart_printf(UART_0_INST, "纯Y轴运动：%.2f°\r\n", angle_y);
    } else if(y_pulse == 0) {
        // 只有X轴运动
        x_speed = base_speed;
        y_speed = 1;  // 最小速度，避免为0
        uart_printf(UART_0_INST, "纯X轴运动：%.2f°\r\n", angle_x);
    } else {
        // 两轴都运动，按距离比例分配速度
        uint32_t max_pulse = (x_pulse > y_pulse) ? x_pulse : y_pulse;

        // 距离大的轴使用最大速度，距离小的轴按比例降速
        x_speed = (uint16_t)((uint32_t)base_speed * x_pulse / max_pulse);
        y_speed = (uint16_t)((uint32_t)base_speed * y_pulse / max_pulse);

        // 确保速度不为0
        if(x_speed < 1) x_speed = 1;
        if(y_speed < 1) y_speed = 1;

        // 计算运动时间（用于验证同步）
        float time_x = (float)x_pulse / x_speed;
        float time_y = (float)y_pulse / y_speed;

        uart_printf(UART_0_INST, "同步运动：X=%.2f°(%d脉冲,速度%d), Y=%.2f°(%d脉冲,速度%d)\r\n",
                   angle_x, x_pulse, x_speed, angle_y, y_pulse, y_speed);
        uart_printf(UART_0_INST, "预计时间：X=%.2fs, Y=%.2fs\r\n", time_x, time_y);
    }

    // 发送X轴电机控制命令 (ID=1, 相对位置模式, 启用同步)
    motor_distance(0x01, x_dir, x_speed, accel, x_pulse, 0, 1);

    // 发送Y轴电机控制命令 (ID=2, 相对位置模式, 启用同步)
    motor_distance(0x02, y_dir, y_speed, accel, y_pulse, 0, 1);

    // 发送同步命令，两个电机同时开始运动
    motor_sync();
}

// 运动学参数配置
#define KINEMATIC_DISTANCE   0.9f    // 固定目标距离(米)
#define PI                   3.14159265359f
#define DEG_TO_RAD          (PI / 180.0f)
#define RAD_TO_DEG          (180.0f / PI)

/**
 * @brief 运动学正解：从当前角度计算当前指向的坐标
 * @param current_x 输出当前X坐标(米，高度方向)
 * @param current_y 输出当前Y坐标(米，水平方向)
 */
void get_current_target_coordinate(float* current_x, float* current_y) {
    // 获取当前角度并转换为弧度
    float pan_rad = Motor_Cur_Pos[0] * DEG_TO_RAD;   // 水平角(弧度)
    float tilt_rad = Motor_Cur_Pos[1] * DEG_TO_RAD;  // 俯仰角(弧度)

    // 运动学正解公式
    *current_y = KINEMATIC_DISTANCE * tanf(pan_rad);   // 水平坐标 = D * tan(θ)
    *current_x = KINEMATIC_DISTANCE * tanf(tilt_rad);  // 高度坐标 = D * tan(φ)

    uart_printf(UART_0_INST, "当前角度: 水平=%.2f°, 俯仰=%.2f°\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
    uart_printf(UART_0_INST, "当前指向坐标: X=%.3fm, Y=%.3fm\r\n", *current_x, *current_y);
}

/**
 * @brief 运动学逆解：从目标坐标计算需要的角度
 * @param target_x 目标X坐标(米，高度方向)
 * @param target_y 目标Y坐标(米，水平方向)
 * @param target_pan 输出目标水平角(度)
 * @param target_tilt 输出目标俯仰角(度)
 */
void calculate_target_angles(float target_x, float target_y, float* target_pan, float* target_tilt) {
    // 运动学逆解公式
    float pan_rad = atan2f(target_y, KINEMATIC_DISTANCE);   // θ = atan2(y, D)
    float tilt_rad = atan2f(target_x, KINEMATIC_DISTANCE);  // φ = atan2(x, D)

    // 弧度转角度
    *target_pan = pan_rad * RAD_TO_DEG;
    *target_tilt = tilt_rad * RAD_TO_DEG;

    uart_printf(UART_0_INST, "目标坐标: X=%.3fm, Y=%.3fm\r\n", target_x, target_y);
    uart_printf(UART_0_INST, "计算角度: 水平=%.2f°, 俯仰=%.2f°\r\n", *target_pan, *target_tilt);
}

/**
 * @brief 移动到目标坐标(基于运动学解算)
 * @param target_x 目标X坐标(米，高度方向)
 * @param target_y 目标Y坐标(米，水平方向)
 * @param speed 移动速度百分比(1-100)
 */
void move_to_coordinate(float target_x, float target_y, uint8_t speed) {
    float target_pan, target_tilt;

    // 运动学逆解：坐标转角度
    calculate_target_angles(target_x, target_y, &target_pan, &target_tilt);

    // 计算相对移动量
    float move_pan = target_pan - Motor_Cur_Pos[0];   // 水平相对移动
    float move_tilt = target_tilt - Motor_Cur_Pos[1]; // 俯仰相对移动

    uart_printf(UART_0_INST, "相对移动: 水平=%.2f°, 俯仰=%.2f°\r\n", move_pan, move_tilt);

    // 执行移动
    motor_move_angle(move_pan, move_tilt, speed, 50);
}

/**
 * @brief 移动到目标角度(绝对角度)
 * @param target_pan 目标水平角(度)
 * @param target_tilt 目标俯仰角(度)
 * @param speed 移动速度百分比(1-100)
 */
void move_to_angle(float target_pan, float target_tilt, uint8_t speed) {
    // 计算相对移动量
    float move_pan = target_pan - Motor_Cur_Pos[0];
    float move_tilt = target_tilt - Motor_Cur_Pos[1];

    // 运动学正解：显示目标坐标(调试用)
    float target_x = KINEMATIC_DISTANCE * tanf(target_tilt * DEG_TO_RAD);
    float target_y = KINEMATIC_DISTANCE * tanf(target_pan * DEG_TO_RAD);

    uart_printf(UART_0_INST, "目标角度: 水平=%.2f°, 俯仰=%.2f°\r\n", target_pan, target_tilt);
    uart_printf(UART_0_INST, "对应坐标: X=%.3fm, Y=%.3fm\r\n", target_x, target_y);
    uart_printf(UART_0_INST, "相对移动: 水平=%.2f°, 俯仰=%.2f°\r\n", move_pan, move_tilt);

    // 执行移动
    motor_move_angle(move_pan, move_tilt, speed, 50);
}

/**
 * @brief 检查是否到达目标坐标
 * @param target_x 目标X坐标(米)
 * @param target_y 目标Y坐标(米)
 * @param tolerance_x X轴容差(米)
 * @param tolerance_y Y轴容差(米)
 * @return 1-已到达，0-未到达
 */
uint8_t check_coordinate_reached(float target_x, float target_y, float tolerance_x, float tolerance_y) {
    float current_x, current_y;

    // 获取当前指向坐标
    get_current_target_coordinate(&current_x, &current_y);

    // 计算误差
    float error_x = fabsf(current_x - target_x);
    float error_y = fabsf(current_y - target_y);

    uart_printf(UART_0_INST, "坐标误差: X=%.3fm, Y=%.3fm\r\n", error_x, error_y);
    uart_printf(UART_0_INST, "允许容差: X=%.3fm, Y=%.3fm\r\n", tolerance_x, tolerance_y);

    if(error_x <= tolerance_x && error_y <= tolerance_y) {
        uart_printf(UART_0_INST, "✓ 已到达目标坐标\r\n");
        return 1;
    } else {
        uart_printf(UART_0_INST, "✗ 未到达目标坐标\r\n");
        return 0;
    }
}
