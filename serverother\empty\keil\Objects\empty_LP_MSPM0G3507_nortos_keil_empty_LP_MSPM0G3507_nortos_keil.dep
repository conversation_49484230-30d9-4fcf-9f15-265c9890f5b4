Dependencies for Project 'empty_LP_MSPM0G3507_nortos_keil', Target 'empty_LP_MSPM0G3507_nortos_keil': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (../empty.c)(0x6885AA67)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MMD)
I (..\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\scheduler.h)(0x686C97C6)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\Key_App.h)(0x687DF41F)
I (APP\motor_app.h)(0x688198CD)
I (APP\Task_func.h)(0x687DF41F)
I (APP\sine_wave.h)(0x6881E58B)
F (../empty.syscfg)(0x6885AA47)()
F (startup_mspm0g350x_uvision.s)(0x67C10126)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-IF:/danpianji/keil/ARM/CMSIS/Include

-Wa,armasm,--pd,"__UVISION_VERSION SETA 542" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x687C50EE)()
F (../ti_msp_dl_config.c)(0x687C50EE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
F (.\APP\mydefine.c)(0x68381AF6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/mydefine.o -MMD)
F (.\APP\Key_App.c)(0x6885D6B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/key_app.o -MMD)
I (APP\Key_App.h)(0x687DF41F)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\scheduler.h)(0x686C97C6)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\motor_app.h)(0x688198CD)
I (APP\Task_func.h)(0x687DF41F)
I (APP\sine_wave.h)(0x6881E58B)
F (.\APP\motor_app.c)(0x6885C026)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor_app.o -MMD)
I (APP\motor_app.h)(0x688198CD)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\scheduler.h)(0x686C97C6)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\Key_App.h)(0x687DF41F)
I (APP\Task_func.h)(0x687DF41F)
I (APP\sine_wave.h)(0x6881E58B)
I (APP\pid_mid.h)(0x6874C311)
F (.\APP\motor_mid.c)(0x6885DABC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor_mid.o -MMD)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\scheduler.h)(0x686C97C6)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\Key_App.h)(0x687DF41F)
I (APP\motor_app.h)(0x688198CD)
I (APP\Task_func.h)(0x687DF41F)
I (APP\sine_wave.h)(0x6881E58B)
F (.\APP\pid_mid.c)(0x6878551C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/pid_mid.o -MMD)
I (APP\pid_mid.h)(0x6874C311)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\scheduler.h)(0x686C97C6)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\Key_App.h)(0x687DF41F)
I (APP\motor_app.h)(0x688198CD)
I (APP\Task_func.h)(0x687DF41F)
I (APP\sine_wave.h)(0x6881E58B)
F (.\APP\scheduler.c)(0x68858DE2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/scheduler.o -MMD)
I (APP\scheduler.h)(0x686C97C6)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\Key_App.h)(0x687DF41F)
I (APP\motor_app.h)(0x688198CD)
I (APP\Task_func.h)(0x687DF41F)
I (APP\sine_wave.h)(0x6881E58B)
F (.\APP\sine_wave.c)(0x6885905E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/sine_wave.o -MMD)
I (APP\motor_app.h)(0x688198CD)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\scheduler.h)(0x686C97C6)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\Key_App.h)(0x687DF41F)
I (APP\Task_func.h)(0x687DF41F)
I (APP\sine_wave.h)(0x6881E58B)
I (APP\pid_mid.h)(0x6874C311)
F (.\APP\Task_func.c)(0x6885DABD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/task_func.o -MMD)
I (APP\Task_func.h)(0x687DF41F)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\scheduler.h)(0x686C97C6)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\Key_App.h)(0x687DF41F)
I (APP\motor_app.h)(0x688198CD)
I (APP\sine_wave.h)(0x6881E58B)
F (.\APP\usart_mid.c)(0x6885CE08)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ../hardware/delay -I ../hardware/Motor_drive -I ../hardware/uart -I ../app -I ../middle -I ../keil -I ./Objects -I ./APP

-IF:/danpianji/keil/ARM/CMSIS/Include

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/usart_mid.o -MMD)
I (APP\usart_mid.h)(0x687DD9CD)
I (APP\mydefine.h)(0x6885C76F)
I (..\..\empty\ti_msp_dl_config.h)(0x687C50EE)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10124)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10124)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10124)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10124)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10124)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10124)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10124)
I (APP\scheduler.h)(0x686C97C6)
I (APP\motor_mid.h)(0x6885D3ED)
I (APP\gimbal_example.h)(0x6885C80E)
I (APP\Key_App.h)(0x687DF41F)
I (APP\motor_app.h)(0x688198CD)
I (APP\Task_func.h)(0x687DF41F)
I (APP\sine_wave.h)(0x6881E58B)
