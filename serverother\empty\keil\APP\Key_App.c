#include "Key_App.h"
#include "motor_app.h" // 包含normalize_angle_diff函数
#include "Task_func.h" // 包含open变量声明
#include "sine_wave.h" // 包含正弦波绘制函数

uint8_t count_Tt=0;
extern uint8_t open;  // 声明为外部变量，在Task_func.c中定义

float angle_error=0.0f;
float reference_yaw = 0.0f; // 零度参考角
float count_return=0;
uint16_t xy_z[5][2]={0};

uint8_t Key_Read(void)
{
	uint8_t Key_num=0;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_21) == 0)Key_num=1;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_24) == 0)Key_num=2;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_23) == 0)Key_num=3;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_27) == 0)Key_num=4;
	return Key_num;
}

uint8_t Key_Down,Key_Up,Key_Val,Key_Old;

/**
 * @brief 更新angle_error为相对于参考角的角度差值
 */

uint32_t began_time=0;
uint8_t Key_Count=0;

void Key_Proc(void)
{
	float current_x, current_y;
	Key_Val=Key_Read();
	Key_Down=Key_Val&(Key_Val^Key_Old);
	Key_Up=~Key_Val&(Key_Val^Key_Old);
	Key_Old=Key_Val;

	switch(Key_Down)
	{
		case 1:
			move_to_coordinate(0,0,40);
			break;
		case 2:
			break;
		case 3:
				//state_up();
			//	motor_move_angle(0, -20, 30, 50);
			motor_distance(0x02, 0, 20, 50, 200, 0, 0); 
		 	//move_to_coordinate(0.4,0.1,40);
			//state_up();
			//	uart_printf(UART_0_INST, "(%.2f°, %.2f°)\r\n", Motor_Cur_Pos[0], Motor_Cur_Pos[1]);

			break;
		case 4:
				
//				xy_z[Key_Count][0]=current_x;
//				xy_z[Key_Count][1]=current_y;//0  1 2 3
//				if(++Key_Count==4)
//				{
//					xy_z[4][0]=xy_z[0][0];
//					xy_z[4][1]=xy_z[0][1];
//					uart_printf(UART_0_INST, "set1:(%d,%d) set2:(%d,%d)\r\n", xy_z[0][0], xy_z[0][1], xy_z[1][0], xy_z[1][1]);              
//					uart_printf(UART_0_INST, "set3:(%d,%d) set4:(%d,%d)\r\n", xy_z[2][0], xy_z[2][1], xy_z[3][0], xy_z[3][1]);
//					uart_printf(UART_0_INST, "P5:X=%d, Y=%d\r\n", xy_z[4][0], xy_z[4][1]);
//					state_data=1;
//					motor_init();
//				}


			break;
	}

	// 正弦波绘制功能已移至按键3控制

}


