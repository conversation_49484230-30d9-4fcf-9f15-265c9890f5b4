#include "Key_App.h"
#include "motor_app.h" // 包含normalize_angle_diff函数
#include "Task_func.h" // 包含open变量声明
#include "sine_wave.h" // 包含正弦波绘制函数

uint8_t count_Tt=0;
extern uint8_t open;  // 声明为外部变量，在Task_func.c中定义

float angle_error=0.0f;
float reference_yaw = 0.0f; // 零度参考角
float count_return=0;
uint16_t xy_z[5][2]={0};

uint8_t Key_Read(void)
{
	uint8_t Key_num=0;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_21) == 0)Key_num=1;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_24) == 0)Key_num=2;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_23) == 0)Key_num=3;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_27) == 0)Key_num=4;
	return Key_num;
}

uint8_t Key_Down,Key_Up,Key_Val,Key_Old;

/**
 * @brief 更新angle_error为相对于参考角的角度差值
 */

uint32_t began_time=0;
uint8_t Key_Count=0;

void example_pixel_coordinate_control(void)
{
    uart_printf(UART_0_INST, "=== 示例2：像素坐标控制 ===\r\n");
    
    // 移动到图像中心
    uart_printf(UART_0_INST, "移动到图像中心(320,240)\r\n");
    move_to_pixel(320, 240, 30);
    delay_ms(2000);
    
//    // 移动到图像左上角
//    uart_printf(UART_0_INST, "移动到左上角(160,120)\r\n");
//    move_to_pixel(160, 120, 30);
//    delay_ms(2000);
    
    // 移动到图像右下角
    uart_printf(UART_0_INST, "移动到右下角(480,360)\r\n");
    move_to_pixel(480, 360, 30);
    delay_ms(2000);
//    
//    // 回到中心
//    uart_printf(UART_0_INST, "回到中心\r\n");
//    move_to_pixel(320, 240, 30);
}

void Key_Proc(void)
{
	Key_Val=Key_Read();
	Key_Down=Key_Val&(Key_Val^Key_Old);
	Key_Up=~Key_Val&(Key_Val^Key_Old);
	Key_Old=Key_Val;

	switch(Key_Down)
	{
		case 1:
				//Motor_Set_Speed(10,0);
				example_pixel_coordinate_control();
	

		//state_up();
			break;
		case 2:
			break;
		case 3:
				motor_stop();
				uart_printf(UART_0_INST, "%.2f deg%.2f deg\r\n",Motor_Cur_Pos[0],Motor_Cur_Pos[1]);
			// 按键3: 统一运动控制 (三角形/正弦波)
//			uart_printf(UART_0_INST, "按键3按下，当前状态: open=%d, motion_mode=%d\r\n", open, motion_mode);
//			if(open==0)
//			{
//				// 开始运动
//				control_mode = 1;  // 切换到统一运动模式
//				open = 1;
//				uart_printf(UART_0_INST, "设置control_mode=1, open=1\r\n");
//				start_motion();    // 根据当前motion_mode开始对应运动
//				uart_printf(UART_0_INST, "开始运动完成\r\n");
//			}
//			else if(open==1)
//			{
//				// 停止所有运动
//				open=0;
//				control_mode = 0;  // 切换回普通模式
//				stop_motion();     // 停止所有运动
//				uart_printf(UART_0_INST, "停止运动\r\n");
//			}
			break;
		case 4:
					motor_move_angle(-Motor_Cur_Pos[0],-Motor_Cur_Pos[1],40,0);
//				xy_z[Key_Count][0]=current_x;
//				xy_z[Key_Count][1]=current_y;//0  1 2 3
//				if(++Key_Count==4)
//				{
//					xy_z[4][0]=xy_z[0][0];
//					xy_z[4][1]=xy_z[0][1];
//					uart_printf(UART_0_INST, "set1:(%d,%d) set2:(%d,%d)\r\n", xy_z[0][0], xy_z[0][1], xy_z[1][0], xy_z[1][1]);              
//					uart_printf(UART_0_INST, "set3:(%d,%d) set4:(%d,%d)\r\n", xy_z[2][0], xy_z[2][1], xy_z[3][0], xy_z[3][1]);
//					uart_printf(UART_0_INST, "P5:X=%d, Y=%d\r\n", xy_z[4][0], xy_z[4][1]);
//					state_data=1;
//					motor_init();
//				}


			break;
	}

	// 正弦波绘制功能已移至按键3控制

}


