#ifndef _GIMBAL_TARGET_CONTROL_H_
#define _GIMBAL_TARGET_CONTROL_H_

#include "mydefine.h"

/**
 * @file gimbal_target_control.h
 * @brief 云台目标点控制系统头文件
 * 
 * 基于目标点坐标计算云台角度，实现精确的目标跟踪控制
 */

// 核心计算函数
void calculate_angles(float x, float y, float D, float* theta, float* phi);  // 计算目标角度

// 配置函数
void gimbal_set_target_distance(float distance);                            // 设置目标距离
void gimbal_set_height_offset(float height_offset);                         // 设置高度偏移
float gimbal_get_target_distance(void);                                     // 获取目标距离

// 移动控制函数
void gimbal_move_to_target(float target_x, float target_y, uint8_t speed);  // 移动到目标坐标
void gimbal_move_to_absolute_angle(float pan_deg, float tilt_deg, uint8_t speed); // 移动到绝对角度
void gimbal_move_to_pixel_target(int pixel_x, int pixel_y, uint8_t speed);  // 像素坐标移动

// 坐标转换函数
void pixel_to_target_coordinate(int pixel_x, int pixel_y, float* target_x, float* target_y); // 像素转目标坐标

// 测试和示例函数
void gimbal_target_control_test(void);                                      // 目标控制测试
void gimbal_calculate_example(void);                                        // 计算示例

#endif
