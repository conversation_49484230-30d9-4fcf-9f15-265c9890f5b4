## Example Summary

云台开环控制系统 (Gimbal Open-Loop Control System)
本项目实现了2轴云台的开环角度控制，包含角度到像素坐标的转换功能。

## 功能特性

- **开环角度控制**：直接通过角度控制云台运动，无需像素反馈
- **角度像素转换**：支持云台角度与图像像素坐标的双向转换
- **可配置参数**：支持视场角、图像分辨率等参数的动态配置
- **多种控制模式**：绝对角度、相对角度、像素坐标控制
- **轨迹规划**：支持扫描、圆形轨迹等复杂运动模式

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  |  |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## 云台控制系统使用说明

### 核心功能

#### 1. 角度到像素转换
```c
// 角度转像素坐标
int pixel_x = angle_to_pixel_x(15.0f);    // 水平15度转像素X坐标
int pixel_y = angle_to_pixel_y(-10.0f);   // 垂直-10度转像素Y坐标

// 像素坐标转角度
float angle_x = pixel_to_angle_x(400);    // 像素X=400转水平角度
float angle_y = pixel_to_angle_y(200);    // 像素Y=200转垂直角度
```

#### 2. 云台控制
```c
// 移动到绝对角度位置
gimbal_move_to_angle(30.0f, 15.0f, 50);   // 移动到(30°,15°)，速度50%

// 移动到像素位置
move_to_pixel(400, 300, 30);              // 移动到像素(400,300)，速度30%

// 相对角度移动
gimbal_move_relative(10.0f, -5.0f, 40);   // 相对移动(+10°,-5°)
```

#### 3. 参数配置
```c
// 设置云台配置参数
gimbal_set_config(
    60.0f,    // 水平视场角(度)
    45.0f,    // 垂直视场角(度)
    640,      // 图像宽度
    480,      // 图像高度
    90.0f,    // 最大水平角度
    45.0f     // 最大垂直角度
);

// 获取当前配置
gimbal_config_t* config = gimbal_get_config();
```

### 默认配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 水平视场角 | 60° | 相机水平视野范围 |
| 垂直视场角 | 45° | 相机垂直视野范围 |
| 图像分辨率 | 640×480 | 图像像素尺寸 |
| 图像中心 | (320,240) | 图像中心像素坐标 |
| 云台水平范围 | ±90° | 云台水平运动范围 |
| 云台垂直范围 | ±45° | 云台垂直运动范围 |
| 电机分辨率 | 3200脉冲/360° | 步进电机控制精度 |

### 坐标系说明

#### 角度坐标系
- **水平角度(Pan)**：正值向右，负值向左
- **垂直角度(Tilt)**：正值向上，负值向下
- **零位**：云台正前方中心位置

#### 像素坐标系
- **原点**：图像左上角(0,0)
- **X轴**：向右为正方向
- **Y轴**：向下为正方向
- **中心**：(320,240)

### 使用示例

#### 基础使用
```c
#include "motor_mid.h"
#include "gimbal_example.h"

int main(void) {
    // 初始化系统
    SYSCFG_DL_init();
    motor_init();

    // 快速测试
    gimbal_quick_test();

    // 运行完整示例
    gimbal_run_examples();

    while(1) {
        // 主循环
    }
}
```

#### 自定义轨迹
```c
void custom_trajectory(void) {
    // 移动到起始位置
    gimbal_move_to_angle(0.0f, 0.0f, 30);
    delay_ms(1000);

    // Z字形扫描
    for(int i = 0; i < 5; i++) {
        gimbal_move_to_angle(-30.0f + i*15.0f, 20.0f, 40);
        delay_ms(500);
        gimbal_move_to_angle(-30.0f + i*15.0f, -20.0f, 40);
        delay_ms(500);
    }
}
```

### 注意事项

1. **角度限制**：确保输入角度在云台物理运动范围内
2. **速度控制**：速度参数范围1-100%，建议30-50%用于精确定位
3. **参数调整**：根据实际相机参数调整FOV和分辨率设置
4. **坐标转换**：注意像素坐标系Y轴向下，角度坐标系Y轴向上
5. **开环控制**：无位置反馈，依赖电机步进精度

### 故障排除

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 角度转换不准确 | FOV参数不匹配 | 调整gimbal_set_config()中的FOV参数 |
| 云台运动范围受限 | 角度超出限制 | 检查max_pan_deg和max_tilt_deg设置 |
| 像素坐标超出范围 | 图像尺寸设置错误 | 确认image_width和image_height参数 |
| 电机不响应 | 通信问题 | 检查UART连接和motor_init()调用 |

## Example Usage

编译、加载并运行示例程序。使用串口监视器查看测试输出和运动状态。
