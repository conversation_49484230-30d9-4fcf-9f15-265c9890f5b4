////#include "motor_mid.h"
////#include "usart_mid.h"
////#include "mydefine.h"

/////**
//// * @file gimbal_example.c
//// * @brief 云台开环控制使用示例
//// *
//// * 本文件展示如何使用云台角度到像素转换功能进行开环控制
//// */

////// 简单的数学函数实现
////static float simple_cos(float angle) {
////    // 简化的余弦近似，角度以弧度为单位
////    // 使用泰勒级数前几项：cos(x) ≈ 1 - x²/2! + x⁴/4!
////    float x2 = angle * angle;
////    return 1.0f - x2/2.0f + x2*x2/24.0f;
////}

////static float simple_sin(float angle) {
////    // 简化的正弦近似，角度以弧度为单位
////    // 使用泰勒级数前几项：sin(x) ≈ x - x³/3! + x⁵/5!
////    float x2 = angle * angle;
////    return angle * (1.0f - x2/6.0f + x2*x2/120.0f);
////}

////// 度转弧度
////static float deg_to_rad(float deg) {
////    return deg * 3.14159f / 180.0f;
////}

////// 示例1：基础角度控制
////void example_basic_angle_control(void)
////{
////    uart_printf(UART_0_INST, "=== 示例1：基础角度控制 ===\r\n");
////    
////    // 移动到水平0度，垂直0度（中心位置）
////    uart_printf(UART_0_INST, "移动到中心位置(0°,0°)\r\n");
////    gimbal_move_to_angle(0.0f, 0.0f, 30);
////    delay_ms(2000);
////    
////    // 移动到水平30度，垂直15度
////    uart_printf(UART_0_INST, "移动到(30°,15°)\r\n");
////    gimbal_move_to_angle(30.0f, 15.0f, 30);
////    delay_ms(2000);
////    
////    // 移动到水平-30度，垂直-15度
////    uart_printf(UART_0_INST, "移动到(-30°,-15°)\r\n");
////    gimbal_move_to_angle(-30.0f, -15.0f, 30);
////    delay_ms(2000);
////    
////    // 回到中心
////    uart_printf(UART_0_INST, "回到中心位置\r\n");
////    gimbal_move_to_angle(0.0f, 0.0f, 30);
////}

//// 示例2：像素坐标控制（调试版）
//void example_pixel_coordinate_control(void)
//{
//    uart_printf(UART_0_INST, "=== 示例2：像素坐标控制（调试版） ===\r\n");

//    // 显示各个位置对应的角度
//    float center_x = pixel_to_angle_x(320);
//    float center_y = pixel_to_angle_y(240);
//    uart_printf(UART_0_INST, "中心(320,240) -> 角度(%.2f°,%.2f°)\r\n", center_x, center_y);

//    float left_top_x = pixel_to_angle_x(160);
//    float left_top_y = pixel_to_angle_y(120);
//    uart_printf(UART_0_INST, "左上(160,120) -> 角度(%.2f°,%.2f°)\r\n", left_top_x, left_top_y);

//    // 检查角度是否在范围内
//    if(left_top_x < -30.0f || left_top_x > 30.0f || left_top_y < -20.0f || left_top_y > 20.0f) {
//        uart_printf(UART_0_INST, "警告：左上角角度超出云台范围！\r\n");
//    }

//    // 移动到图像中心
//    uart_printf(UART_0_INST, "\n步骤1：移动到图像中心(320,240)\r\n");
//    move_to_pixel(320, 240, 20);  // 降低速度便于观察
//    delay_ms(3000);  // 增加延时

//    // 移动到图像左上角
//    uart_printf(UART_0_INST, "步骤2：移动到左上角(160,120)\r\n");
//    move_to_pixel(160, 120, 20);  // 降低速度便于观察
//    delay_ms(3000);  // 增加延时

//    // 回到中心
//    uart_printf(UART_0_INST, "步骤3：回到中心(320,240)\r\n");
//    move_to_pixel(320, 240, 20);  // 降低速度便于观察
//    delay_ms(3000);  // 增加延时

//    uart_printf(UART_0_INST, "运动序列完成\r\n");
//}

////// 示例3：扫描模式
////void example_scan_pattern(void)
////{
////    uart_printf(UART_0_INST, "=== 示例3：扫描模式 ===\r\n");
////    
////    // 水平扫描
////    uart_printf(UART_0_INST, "水平扫描\r\n");
////    for(int x = 160; x <= 480; x += 40) {
////        move_to_pixel(x, 240, 50);
////        delay_ms(500);
////    }
////    
////    // 垂直扫描
////    uart_printf(UART_0_INST, "垂直扫描\r\n");
////    for(int y = 120; y <= 360; y += 30) {
////        move_to_pixel(320, y, 50);
////        delay_ms(500);
////    }
////    
////    // 回到中心
////    move_to_pixel(320, 240, 30);
////}

////// 示例4：圆形轨迹
////void example_circular_trajectory(void)
////{
////    uart_printf(UART_0_INST, "=== 示例4：圆形轨迹 ===\r\n");
////    
////    float center_x = 0.0f;  // 中心角度
////    float center_y = 0.0f;
////    float radius = 15.0f;   // 半径（度）
////    int steps = 16;         // 步数
////    
////    for(int i = 0; i < steps; i++) {
////        float angle = 2.0f * 3.14159f * i / steps;
////        float x_angle = center_x + radius * simple_cos(angle);
////        float y_angle = center_y + radius * simple_sin(angle);
////        
////        uart_printf(UART_0_INST, "圆形轨迹点%d: (%.1f°,%.1f°)\r\n", i+1, x_angle, y_angle);
////        gimbal_move_to_angle(x_angle, y_angle, 40);
////        delay_ms(800);
////    }
////    
////    // 回到中心
////    gimbal_move_to_angle(0.0f, 0.0f, 30);
////}

////// 示例5：配置参数调整
////void example_config_adjustment(void)
////{
////    uart_printf(UART_0_INST, "=== 示例5：配置参数调整 ===\r\n");
////    
////    // 显示当前配置
////    gimbal_config_t* config = gimbal_get_config();
////    uart_printf(UART_0_INST, "当前配置:\r\n");
////    uart_printf(UART_0_INST, "  水平FOV: %.1f°\r\n", config->fov_horizontal_deg);
////    uart_printf(UART_0_INST, "  垂直FOV: %.1f°\r\n", config->fov_vertical_deg);
////    uart_printf(UART_0_INST, "  图像尺寸: %dx%d\r\n", config->image_width, config->image_height);
////    
////    // 测试转换
////    gimbal_test_conversion();
////    
////    // 修改配置（例如：更大的视场角）
////    uart_printf(UART_0_INST, "\n修改配置为更大视场角...\r\n");
////    gimbal_set_config(90.0f, 60.0f, 640, 480, 90.0f, 45.0f);
////    
////    // 再次测试
////    uart_printf(UART_0_INST, "\n新配置下的转换:\r\n");
////    gimbal_test_conversion();
////    
////    // 恢复默认配置
////    gimbal_set_config(60.0f, 45.0f, 640, 480, 90.0f, 45.0f);
////}

////// 示例6：相对移动
////void example_relative_movement(void)
////{
////    uart_printf(UART_0_INST, "=== 示例6：相对移动 ===\r\n");
////    
////    // 先移动到中心
////    gimbal_move_to_angle(0.0f, 0.0f, 30);
////    delay_ms(1000);
////    
////    // 相对移动：向右10度
////    uart_printf(UART_0_INST, "相对移动：向右10°\r\n");
////    gimbal_move_relative(10.0f, 0.0f, 30);
////    delay_ms(1000);
////    
////    // 相对移动：向上5度
////    uart_printf(UART_0_INST, "相对移动：向上5°\r\n");
////    gimbal_move_relative(0.0f, 5.0f, 30);
////    delay_ms(1000);
////    
////    // 相对移动：向左20度，向下10度
////    uart_printf(UART_0_INST, "相对移动：向左20°，向下10°\r\n");
////    gimbal_move_relative(-20.0f, -10.0f, 30);
////    delay_ms(1000);
////    
////    // 回到中心
////    gimbal_move_to_angle(0.0f, 0.0f, 30);
////}

////// 主示例函数
////void gimbal_run_examples(void)
////{
////    uart_printf(UART_0_INST, "\n========== 云台开环控制示例 ==========\r\n");
////    
////    // 初始化云台
////    motor_init();
////    delay_ms(1000);
////    
////    // 运行各个示例
////    example_basic_angle_control();
////    delay_ms(2000);
////    
////    example_pixel_coordinate_control();
////    delay_ms(2000);
////    
////    example_scan_pattern();
////    delay_ms(2000);
////    
////    example_circular_trajectory();
////    delay_ms(2000);
////    
////    example_config_adjustment();
////    delay_ms(2000);
////    
////    example_relative_movement();
////    
////    uart_printf(UART_0_INST, "\n========== 示例演示完成 ==========\r\n");
////}



////// 中心点标定 - 将当前位置设为中心点
////void gimbal_center_calibration(int current_pixel_x, int current_pixel_y) 
////{
////    uart_printf(UART_0_INST, "=== 中心点标定 ===\r\n");
////    uart_printf(UART_0_INST, "当前像素坐标: (%d,%d)\r\n", current_pixel_x, current_pixel_y);

////    // 更新配置中心点
////    gimbal_set_center(current_pixel_x, current_pixel_y);

////    uart_printf(UART_0_INST, "标定完成！当前位置已设为中心点\r\n");
////}



////// 按键触发标定 - 当key_down=1时调用
////void gimbal_key_calibration(uint8_t key_down, int current_x, int current_y) {
////    static uint8_t calibration_active = 0;

////    if(key_down == 1 && calibration_active == 0) {
////        calibration_active = 1;  // 防止重复触发

////        uart_printf(UART_0_INST, "\n*** 按键触发中心点标定 ***\r\n");
////        gimbal_center_calibration(current_x, current_y);

////        calibration_active = 0;  // 标定完成，允许下次触发
////    }
////}

////// 简单的测试函数
////void gimbal_quick_test(void)
////{
////    uart_printf(UART_0_INST, "云台快速测试...\r\n");
////    
////    // 测试角度到像素转换
////    int px = angle_to_pixel_x(15.0f);
////    int py = angle_to_pixel_y(-10.0f);
////    uart_printf(UART_0_INST, "角度(15°,-10°) -> 像素(%d,%d)\r\n", px, py);
////    
////    // 测试像素到角度转换
////    float ax = pixel_to_angle_x(400);
////    float ay = pixel_to_angle_y(180);
////    uart_printf(UART_0_INST, "像素(400,180) -> 角度(%.2f°,%.2f°)\r\n", ax, ay);
////    
////    // 移动测试
////    uart_printf(UART_0_INST, "移动到像素(400,300)\r\n");
////    move_to_pixel(400, 300, 30);
////}

////// 标定使用示例
////void gimbal_calibration_demo(void) {
////    uart_printf(UART_0_INST, "=== 中心点标定演示 ===\r\n");

////    // 示例：假设当前读取到的像素坐标是(315, 235)
////    uart_printf(UART_0_INST, "假设当前读取像素坐标为(315,235)\r\n");
////    gimbal_center_calibration(315, 235);

////    // 测试标定后的转换
////    uart_printf(UART_0_INST, "\n测试标定后的转换:\r\n");
////    float angle_x = pixel_to_angle_x(315);  // 应该接近0度
////    float angle_y = pixel_to_angle_y(235);  // 应该接近0度
////    uart_printf(UART_0_INST, "像素(315,235) -> 角度(%.2f°,%.2f°)\r\n", angle_x, angle_y);

////    int pixel_x = angle_to_pixel_x(0.0f);   // 应该是315
////    int pixel_y = angle_to_pixel_y(0.0f);   // 应该是235
////    uart_printf(UART_0_INST, "角度(0°,0°) -> 像素(%d,%d)\r\n", pixel_x, pixel_y);

////    // 恢复默认配置
////    uart_printf(UART_0_INST, "\n恢复默认配置\r\n");
////    gimbal_set_center(320, 240);
////}

//// 诊断像素坐标控制问题的函数
//void diagnose_pixel_control_issue(void) {
//    // 计算关键位置的角度
//    float center_angle_x = pixel_to_angle_x(320);
//    float center_angle_y = pixel_to_angle_y(240);

//    float left_top_angle_x = pixel_to_angle_x(160);
//    float left_top_angle_y = pixel_to_angle_y(120);

//    // 检查角度范围
//    if(left_top_angle_x < -30.0f || left_top_angle_x > 30.0f) {
//        return; // 角度超出水平范围
//    }
//    if(left_top_angle_y < -20.0f || left_top_angle_y > 20.0f) {
//        return; // 角度超出垂直范围
//    }

//    // 执行慢速运动测试
//    // 步骤1：移动到中心
//    gimbal_move_to_angle(center_angle_x, center_angle_y, 15);  // 很慢的速度
//    delay_ms(4000);

//    // 步骤2：移动到左上角
//    gimbal_move_to_angle(left_top_angle_x, left_top_angle_y, 15);
//    delay_ms(4000);

//    // 步骤3：回到中心
//    gimbal_move_to_angle(center_angle_x, center_angle_y, 15);
//    delay_ms(4000);
//}


