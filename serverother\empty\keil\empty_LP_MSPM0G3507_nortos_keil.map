Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to usart_mid.o(.text.UART_DMA_Init) for UART_DMA_Init
    empty.o(.text.main) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    empty.o(.text.main) refers to scheduler.o(.text.SystemTime_Init) for SystemTime_Init
    empty.o(.text.main) refers to motor_app.o(.text.app_pid_init) for app_pid_init
    empty.o(.text.main) refers to motor_mid.o(.text.motor_init) for motor_init
    empty.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to scheduler.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to usart_mid.o(.text.UART3_IRQHandler) for UART3_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to usart_mid.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to usart_mid.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for SYSCFG_DL_UART_2_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) for SYSCFG_DL_MCAN0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gUART_2Backup) for gUART_2Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gMCAN0Backup) for gMCAN0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_MCAN_reset) for DL_MCAN_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_MCAN_enablePower) for DL_MCAN_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for DL_GPIO_initPeripheralAnalogFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for DL_GPIO_initDigitalInputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for DL_SYSCTL_enableMFCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK) for DL_SYSCTL_enableMFPCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource) for DL_SYSCTL_setMFPCLKSource
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for DL_UART_enableDMAReceiveEvent
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for DL_UART_enableFIFOs
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for DL_UART_setRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for DL_UART_setTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for DL_UART_enableDMAReceiveEvent
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for DL_UART_enableDMAReceiveEvent
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for DL_UART_enableFIFOs
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for DL_UART_setRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for DL_UART_setTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for SYSCFG_DL_DMA_CH0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH2_init) for SYSCFG_DL_DMA_CH2_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH1_init) for SYSCFG_DL_DMA_CH1_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SysTick_Config) for SysTick_Config
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to ti_msp_dl_config.o(.text.DL_MCAN_enableModuleClock) for DL_MCAN_enableModuleClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for DL_MCAN_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for DL_MCAN_getRevisionId
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for DL_MCAN_isMemInitDone
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for DL_MCAN_setOpMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for DL_MCAN_getOpMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_init) for DL_MCAN_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for DL_MCAN_setBitTime
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for DL_MCAN_msgRAMConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for DL_MCAN_setExtIDAndMask
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for DL_MCAN_enableIntr
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for DL_MCAN_selectIntrLine
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for DL_MCAN_enableIntrLine
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to ti_msp_dl_config.o(.text.DL_MCAN_clearInterruptStatus) for DL_MCAN_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to ti_msp_dl_config.o(.text.DL_MCAN_enableInterrupt) for DL_MCAN_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to ti_msp_dl_config.o(.rodata.gMCAN0ClockConf) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to ti_msp_dl_config.o(.rodata.gMCAN0InitParams) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to ti_msp_dl_config.o(.rodata.gMCAN0BitTimes) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) refers to ti_msp_dl_config.o(.rodata.gMCAN0MsgRAMConfigParams) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MCAN0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for DL_UART_Main_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for DL_MCAN_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gUART_2Backup) for gUART_2Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gMCAN0Backup) for gMCAN0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for DL_UART_Main_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for DL_MCAN_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gUART_2Backup) for gUART_2Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gMCAN0Backup) for gMCAN0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_reset) refers to ti_msp_dl_config.o(.text.DL_MCAN_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_enablePower) refers to ti_msp_dl_config.o(.text.DL_MCAN_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFPCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setMFPCLKSource) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH2_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH2_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH2Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH2_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH2_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH1_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH1_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SysTick_Config) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.ARM.exidx.text.SysTick_Config) refers to ti_msp_dl_config.o(.text.SysTick_Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_enableModuleClock) refers to ti_msp_dl_config.o(.text.DL_MCAN_enableModuleClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_MCAN_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_MCAN_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    key_app.o(.text.Key_Read) refers to key_app.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key_app.o(.ARM.exidx.text.Key_Read) refers to key_app.o(.text.Key_Read) for [Anonymous Symbol]
    key_app.o(.ARM.exidx.text.DL_GPIO_readPins) refers to key_app.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    key_app.o(.text.example_pixel_coordinate_control) refers to usart_mid.o(.text.uart_printf) for uart_printf
    key_app.o(.text.example_pixel_coordinate_control) refers to motor_mid.o(.text.move_to_pixel) for move_to_pixel
    key_app.o(.text.example_pixel_coordinate_control) refers to scheduler.o(.text.delay_ms) for delay_ms
    key_app.o(.text.example_pixel_coordinate_control) refers to key_app.o(.rodata.str1.1) for [Anonymous Symbol]
    key_app.o(.ARM.exidx.text.example_pixel_coordinate_control) refers to key_app.o(.text.example_pixel_coordinate_control) for [Anonymous Symbol]
    key_app.o(.text.Key_Proc) refers to key_app.o(.text.Key_Read) for Key_Read
    key_app.o(.text.Key_Proc) refers to motor_mid.o(.text.motor_stop) for motor_stop
    key_app.o(.text.Key_Proc) refers to f2d.o(.text) for __aeabi_f2d
    key_app.o(.text.Key_Proc) refers to usart_mid.o(.text.uart_printf) for uart_printf
    key_app.o(.text.Key_Proc) refers to key_app.o(.text.example_pixel_coordinate_control) for example_pixel_coordinate_control
    key_app.o(.text.Key_Proc) refers to key_app.o(.bss.Key_Val) for Key_Val
    key_app.o(.text.Key_Proc) refers to key_app.o(.bss.Key_Old) for Key_Old
    key_app.o(.text.Key_Proc) refers to key_app.o(.bss.Key_Down) for Key_Down
    key_app.o(.text.Key_Proc) refers to key_app.o(.bss.Key_Up) for Key_Up
    key_app.o(.text.Key_Proc) refers to usart_mid.o(.bss.Motor_Cur_Pos) for Motor_Cur_Pos
    key_app.o(.text.Key_Proc) refers to key_app.o(.rodata.str1.1) for [Anonymous Symbol]
    key_app.o(.ARM.exidx.text.Key_Proc) refers to key_app.o(.text.Key_Proc) for [Anonymous Symbol]
    motor_app.o(.text.app_pid_init) refers to fflti.o(.text) for __aeabi_i2f
    motor_app.o(.text.app_pid_init) refers to pid_mid.o(.text.pid_init) for pid_init
    motor_app.o(.text.app_pid_init) refers to motor_app.o(.data.pid_params_x) for pid_params_x
    motor_app.o(.text.app_pid_init) refers to motor_app.o(.bss.current_x) for current_x
    motor_app.o(.text.app_pid_init) refers to motor_app.o(.bss.pid_x) for pid_x
    motor_app.o(.text.app_pid_init) refers to motor_app.o(.data.pid_params_y) for pid_params_y
    motor_app.o(.text.app_pid_init) refers to motor_app.o(.bss.current_y) for current_y
    motor_app.o(.text.app_pid_init) refers to motor_app.o(.bss.pid_y) for pid_y
    motor_app.o(.ARM.exidx.text.app_pid_init) refers to motor_app.o(.text.app_pid_init) for [Anonymous Symbol]
    motor_app.o(.text.app_pid_set_x_params) refers to pid_mid.o(.text.pid_set_params) for pid_set_params
    motor_app.o(.text.app_pid_set_x_params) refers to pid_mid.o(.text.pid_set_limit) for pid_set_limit
    motor_app.o(.text.app_pid_set_x_params) refers to motor_app.o(.data.pid_params_x) for pid_params_x
    motor_app.o(.text.app_pid_set_x_params) refers to motor_app.o(.bss.pid_x) for pid_x
    motor_app.o(.ARM.exidx.text.app_pid_set_x_params) refers to motor_app.o(.text.app_pid_set_x_params) for [Anonymous Symbol]
    motor_app.o(.text.app_pid_set_y_params) refers to pid_mid.o(.text.pid_set_params) for pid_set_params
    motor_app.o(.text.app_pid_set_y_params) refers to pid_mid.o(.text.pid_set_limit) for pid_set_limit
    motor_app.o(.text.app_pid_set_y_params) refers to motor_app.o(.data.pid_params_y) for pid_params_y
    motor_app.o(.text.app_pid_set_y_params) refers to motor_app.o(.bss.pid_y) for pid_y
    motor_app.o(.ARM.exidx.text.app_pid_set_y_params) refers to motor_app.o(.text.app_pid_set_y_params) for [Anonymous Symbol]
    motor_app.o(.text.read_place) refers to motor_app.o(.bss.current_x) for current_x
    motor_app.o(.text.read_place) refers to motor_app.o(.bss.current_y) for current_y
    motor_app.o(.ARM.exidx.text.read_place) refers to motor_app.o(.text.read_place) for [Anonymous Symbol]
    motor_app.o(.text.pid_target) refers to fflti.o(.text) for __aeabi_i2f
    motor_app.o(.text.pid_target) refers to pid_mid.o(.text.pid_set_target) for pid_set_target
    motor_app.o(.text.pid_target) refers to motor_app.o(.data.target_x) for target_x
    motor_app.o(.text.pid_target) refers to motor_app.o(.data.target_y) for target_y
    motor_app.o(.text.pid_target) refers to motor_app.o(.bss.pid_x) for pid_x
    motor_app.o(.text.pid_target) refers to motor_app.o(.bss.pid_y) for pid_y
    motor_app.o(.ARM.exidx.text.pid_target) refers to motor_app.o(.text.pid_target) for [Anonymous Symbol]
    motor_app.o(.text.app_pid_calc) refers to fflti.o(.text) for __aeabi_i2f
    motor_app.o(.text.app_pid_calc) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    motor_app.o(.text.app_pid_calc) refers to usart_mid.o(.text.uart_printf) for uart_printf
    motor_app.o(.text.app_pid_calc) refers to motor_mid.o(.text.motor_stop) for motor_stop
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.text.pid_target) for pid_target
    motor_app.o(.text.app_pid_calc) refers to pid_mid.o(.text.pid_calculate_positional) for pid_calculate_positional
    motor_app.o(.text.app_pid_calc) refers to pid_mid.o(.text.app_pid_limit_integral) for app_pid_limit_integral
    motor_app.o(.text.app_pid_calc) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    motor_app.o(.text.app_pid_calc) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    motor_app.o(.text.app_pid_calc) refers to ffixi.o(.text) for __aeabi_f2iz
    motor_app.o(.text.app_pid_calc) refers to motor_mid.o(.text.Motor_Set_Speed) for Motor_Set_Speed
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.data.target_x) for target_x
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.bss.current_x) for current_x
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.data.target_y) for target_y
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.bss.current_y) for current_y
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.data.pid_params_x) for pid_params_x
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.data.pid_params_y) for pid_params_y
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.bss.pid_x) for pid_x
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.bss.pid_y) for pid_y
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.bss.motor_x) for motor_x
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.bss.motor_y) for motor_y
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.rodata.str1.1) for [Anonymous Symbol]
    motor_app.o(.text.app_pid_calc) refers to task_func.o(.data.state_data) for state_data
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.data.send_count) for send_count
    motor_app.o(.text.app_pid_calc) refers to key_app.o(.bss.xy_z) for xy_z
    motor_app.o(.text.app_pid_calc) refers to task_func.o(.bss.open) for open
    motor_app.o(.text.app_pid_calc) refers to motor_app.o(.bss.xy) for xy
    motor_app.o(.ARM.exidx.text.app_pid_calc) refers to motor_app.o(.text.app_pid_calc) for [Anonymous Symbol]
    motor_app.o(.text.Read_L) refers to usart_mid.o(.text.uart_printf) for uart_printf
    motor_app.o(.text.Read_L) refers to motor_app.o(.bss.current_x) for current_x
    motor_app.o(.text.Read_L) refers to motor_app.o(.bss.current_y) for current_y
    motor_app.o(.text.Read_L) refers to motor_app.o(.rodata.str1.1) for [Anonymous Symbol]
    motor_app.o(.ARM.exidx.text.Read_L) refers to motor_app.o(.text.Read_L) for [Anonymous Symbol]
    motor_app.o(.text.get_data) refers to usart_mid.o(.text.uart_printf) for uart_printf
    motor_app.o(.text.get_data) refers to motor_app.o(.bss.xy) for xy
    motor_app.o(.text.get_data) refers to motor_app.o(.bss.system_flag) for system_flag
    motor_app.o(.text.get_data) refers to motor_app.o(.rodata.str1.1) for [Anonymous Symbol]
    motor_app.o(.ARM.exidx.text.get_data) refers to motor_app.o(.text.get_data) for [Anonymous Symbol]
    motor_mid.o(.text.motor_init) refers to usart_mid.o(.text.uart_send_data) for uart_send_data
    motor_mid.o(.text.motor_init) refers to scheduler.o(.text.delay_ms) for delay_ms
    motor_mid.o(.text.motor_init) refers to motor_mid.o(.text.motor_stop) for motor_stop
    motor_mid.o(.text.motor_init) refers to motor_mid.o(.data.RE) for RE
    motor_mid.o(.ARM.exidx.text.motor_init) refers to motor_mid.o(.text.motor_init) for [Anonymous Symbol]
    motor_mid.o(.text.motor_stop) refers to usart_mid.o(.text.uart_send_data) for uart_send_data
    motor_mid.o(.text.motor_stop) refers to scheduler.o(.text.delay_ms) for delay_ms
    motor_mid.o(.text.motor_stop) refers to motor_mid.o(.data.STP) for STP
    motor_mid.o(.ARM.exidx.text.motor_stop) refers to motor_mid.o(.text.motor_stop) for [Anonymous Symbol]
    motor_mid.o(.text.motor_distance) refers to usart_mid.o(.text.uart_send_data) for uart_send_data
    motor_mid.o(.text.motor_distance) refers to scheduler.o(.text.delay_ms) for delay_ms
    motor_mid.o(.text.motor_distance) refers to motor_mid.o(.data.SE) for SE
    motor_mid.o(.ARM.exidx.text.motor_distance) refers to motor_mid.o(.text.motor_distance) for [Anonymous Symbol]
    motor_mid.o(.text.motor_speed) refers to usart_mid.o(.text.uart_send_data) for uart_send_data
    motor_mid.o(.text.motor_speed) refers to scheduler.o(.text.delay_ms) for delay_ms
    motor_mid.o(.text.motor_speed) refers to motor_mid.o(.data.SE1) for SE1
    motor_mid.o(.ARM.exidx.text.motor_speed) refers to motor_mid.o(.text.motor_speed) for [Anonymous Symbol]
    motor_mid.o(.text.motor_readdistance) refers to usart_mid.o(.text.uart_send_data) for uart_send_data
    motor_mid.o(.text.motor_readdistance) refers to scheduler.o(.text.delay_ms) for delay_ms
    motor_mid.o(.text.motor_readdistance) refers to motor_mid.o(.data.READ) for READ
    motor_mid.o(.ARM.exidx.text.motor_readdistance) refers to motor_mid.o(.text.motor_readdistance) for [Anonymous Symbol]
    motor_mid.o(.text.motor_readspeed) refers to usart_mid.o(.text.uart_send_data) for uart_send_data
    motor_mid.o(.text.motor_readspeed) refers to motor_mid.o(.data.READ_SPEED) for READ_SPEED
    motor_mid.o(.ARM.exidx.text.motor_readspeed) refers to motor_mid.o(.text.motor_readspeed) for [Anonymous Symbol]
    motor_mid.o(.text.motor_clear_position) refers to usart_mid.o(.text.uart_send_data) for uart_send_data
    motor_mid.o(.text.motor_clear_position) refers to scheduler.o(.text.delay_ms) for delay_ms
    motor_mid.o(.text.motor_clear_position) refers to motor_mid.o(.data.CLEAR) for CLEAR
    motor_mid.o(.ARM.exidx.text.motor_clear_position) refers to motor_mid.o(.text.motor_clear_position) for [Anonymous Symbol]
    motor_mid.o(.text.motor_clear_all_position) refers to motor_mid.o(.text.motor_clear_position) for motor_clear_position
    motor_mid.o(.ARM.exidx.text.motor_clear_all_position) refers to motor_mid.o(.text.motor_clear_all_position) for [Anonymous Symbol]
    motor_mid.o(.text.motor_sync) refers to usart_mid.o(.text.uart_send_data) for uart_send_data
    motor_mid.o(.text.motor_sync) refers to scheduler.o(.text.delay_ms) for delay_ms
    motor_mid.o(.text.motor_sync) refers to motor_mid.o(.data.TB) for TB
    motor_mid.o(.ARM.exidx.text.motor_sync) refers to motor_mid.o(.text.motor_sync) for [Anonymous Symbol]
    motor_mid.o(.text.Motor_Set_Speed) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    motor_mid.o(.text.Motor_Set_Speed) refers to motor_mid.o(.text.motor_speed) for motor_speed
    motor_mid.o(.text.Motor_Set_Speed) refers to motor_mid.o(.text.motor_sync) for motor_sync
    motor_mid.o(.ARM.exidx.text.Motor_Set_Speed) refers to motor_mid.o(.text.Motor_Set_Speed) for [Anonymous Symbol]
    motor_mid.o(.text.dis_read) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    motor_mid.o(.text.dis_read) refers to motor_mid.o(.text.motor_readdistance) for motor_readdistance
    motor_mid.o(.text.dis_read) refers to motor_mid.o(.bss.dis_read.read_count) for [Anonymous Symbol]
    motor_mid.o(.ARM.exidx.text.dis_read) refers to motor_mid.o(.text.dis_read) for [Anonymous Symbol]
    motor_mid.o(.text.angle_to_pixel_x) refers to motor_mid.o(.text.constrain_angle) for constrain_angle
    motor_mid.o(.text.angle_to_pixel_x) refers to fdiv.o(.text) for __aeabi_fdiv
    motor_mid.o(.text.angle_to_pixel_x) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    motor_mid.o(.text.angle_to_pixel_x) refers to ffixi.o(.text) for __aeabi_f2iz
    motor_mid.o(.ARM.exidx.text.angle_to_pixel_x) refers to motor_mid.o(.text.angle_to_pixel_x) for [Anonymous Symbol]
    motor_mid.o(.text.constrain_angle) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    motor_mid.o(.text.constrain_angle) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    motor_mid.o(.ARM.exidx.text.constrain_angle) refers to motor_mid.o(.text.constrain_angle) for [Anonymous Symbol]
    motor_mid.o(.text.angle_to_pixel_y) refers to motor_mid.o(.text.constrain_angle) for constrain_angle
    motor_mid.o(.text.angle_to_pixel_y) refers to fdiv.o(.text) for __aeabi_fdiv
    motor_mid.o(.text.angle_to_pixel_y) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    motor_mid.o(.text.angle_to_pixel_y) refers to ffixi.o(.text) for __aeabi_f2iz
    motor_mid.o(.ARM.exidx.text.angle_to_pixel_y) refers to motor_mid.o(.text.angle_to_pixel_y) for [Anonymous Symbol]
    motor_mid.o(.text.pixel_to_angle_x) refers to fflti.o(.text) for __aeabi_i2f
    motor_mid.o(.text.pixel_to_angle_x) refers to fdiv.o(.text) for __aeabi_fdiv
    motor_mid.o(.text.pixel_to_angle_x) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    motor_mid.o(.text.pixel_to_angle_x) refers to motor_mid.o(.text.constrain_angle) for constrain_angle
    motor_mid.o(.ARM.exidx.text.pixel_to_angle_x) refers to motor_mid.o(.text.pixel_to_angle_x) for [Anonymous Symbol]
    motor_mid.o(.text.pixel_to_angle_y) refers to fflti.o(.text) for __aeabi_i2f
    motor_mid.o(.text.pixel_to_angle_y) refers to fdiv.o(.text) for __aeabi_fdiv
    motor_mid.o(.text.pixel_to_angle_y) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    motor_mid.o(.text.pixel_to_angle_y) refers to motor_mid.o(.text.constrain_angle) for constrain_angle
    motor_mid.o(.ARM.exidx.text.pixel_to_angle_y) refers to motor_mid.o(.text.pixel_to_angle_y) for [Anonymous Symbol]
    motor_mid.o(.text.move_to_pixel) refers to motor_mid.o(.text.pixel_to_angle_x) for pixel_to_angle_x
    motor_mid.o(.text.move_to_pixel) refers to motor_mid.o(.text.pixel_to_angle_y) for pixel_to_angle_y
    motor_mid.o(.text.move_to_pixel) refers to motor_mid.o(.text.motor_move_angle) for motor_move_angle
    motor_mid.o(.ARM.exidx.text.move_to_pixel) refers to motor_mid.o(.text.move_to_pixel) for [Anonymous Symbol]
    motor_mid.o(.text.motor_move_angle) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    motor_mid.o(.text.motor_move_angle) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    motor_mid.o(.text.motor_move_angle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    motor_mid.o(.text.motor_move_angle) refers to fdiv.o(.text) for __aeabi_fdiv
    motor_mid.o(.text.motor_move_angle) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    motor_mid.o(.text.motor_move_angle) refers to ffixui.o(.text) for __aeabi_f2uiz
    motor_mid.o(.text.motor_move_angle) refers to motor_mid.o(.text.motor_distance) for motor_distance
    motor_mid.o(.text.motor_move_angle) refers to motor_mid.o(.text.motor_sync) for motor_sync
    motor_mid.o(.ARM.exidx.text.motor_move_angle) refers to motor_mid.o(.text.motor_move_angle) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_set_config) refers to motor_mid.o(.data.gimbal_config) for [Anonymous Symbol]
    motor_mid.o(.ARM.exidx.text.gimbal_set_config) refers to motor_mid.o(.text.gimbal_set_config) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_get_config) refers to motor_mid.o(.data.gimbal_config) for [Anonymous Symbol]
    motor_mid.o(.ARM.exidx.text.gimbal_get_config) refers to motor_mid.o(.text.gimbal_get_config) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_set_center) refers to usart_mid.o(.text.uart_printf) for uart_printf
    motor_mid.o(.text.gimbal_set_center) refers to motor_mid.o(.data.gimbal_config) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_set_center) refers to motor_mid.o(.rodata.str1.1) for [Anonymous Symbol]
    motor_mid.o(.ARM.exidx.text.gimbal_set_center) refers to motor_mid.o(.text.gimbal_set_center) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_move_to_angle) refers to motor_mid.o(.text.motor_move_angle) for motor_move_angle
    motor_mid.o(.ARM.exidx.text.gimbal_move_to_angle) refers to motor_mid.o(.text.gimbal_move_to_angle) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_move_relative) refers to motor_mid.o(.text.motor_move_angle) for motor_move_angle
    motor_mid.o(.ARM.exidx.text.gimbal_move_relative) refers to motor_mid.o(.text.gimbal_move_relative) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_test_conversion) refers to usart_mid.o(.text.uart_printf) for uart_printf
    motor_mid.o(.text.gimbal_test_conversion) refers to f2d.o(.text) for __aeabi_f2d
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.text.angle_to_pixel_x) for angle_to_pixel_x
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.text.angle_to_pixel_y) for angle_to_pixel_y
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.text.pixel_to_angle_x) for pixel_to_angle_x
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.text.pixel_to_angle_y) for pixel_to_angle_y
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.rodata..L__const.gimbal_test_conversion.test_angles) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.rodata..L__const.gimbal_test_conversion.test_pixels_x) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.rodata..L__const.gimbal_test_conversion.test_pixels_y) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.rodata.str1.1) for [Anonymous Symbol]
    motor_mid.o(.text.gimbal_test_conversion) refers to motor_mid.o(.data.gimbal_config) for [Anonymous Symbol]
    motor_mid.o(.ARM.exidx.text.gimbal_test_conversion) refers to motor_mid.o(.text.gimbal_test_conversion) for [Anonymous Symbol]
    pid_mid.o(.text.app_pid_limit_integral) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    pid_mid.o(.text.app_pid_limit_integral) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    pid_mid.o(.ARM.exidx.text.app_pid_limit_integral) refers to pid_mid.o(.text.app_pid_limit_integral) for [Anonymous Symbol]
    pid_mid.o(.ARM.exidx.text.pid_init) refers to pid_mid.o(.text.pid_init) for [Anonymous Symbol]
    pid_mid.o(.ARM.exidx.text.pid_set_target) refers to pid_mid.o(.text.pid_set_target) for [Anonymous Symbol]
    pid_mid.o(.ARM.exidx.text.pid_set_params) refers to pid_mid.o(.text.pid_set_params) for [Anonymous Symbol]
    pid_mid.o(.ARM.exidx.text.pid_set_limit) refers to pid_mid.o(.text.pid_set_limit) for [Anonymous Symbol]
    pid_mid.o(.ARM.exidx.text.pid_reset) refers to pid_mid.o(.text.pid_reset) for [Anonymous Symbol]
    pid_mid.o(.text.pid_calculate_positional) refers to pid_mid.o(.text.pid_formula_positional) for pid_formula_positional
    pid_mid.o(.text.pid_calculate_positional) refers to pid_mid.o(.text.pid_out_limit) for pid_out_limit
    pid_mid.o(.ARM.exidx.text.pid_calculate_positional) refers to pid_mid.o(.text.pid_calculate_positional) for [Anonymous Symbol]
    pid_mid.o(.text.pid_formula_positional) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    pid_mid.o(.text.pid_formula_positional) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    pid_mid.o(.text.pid_formula_positional) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid_mid.o(.ARM.exidx.text.pid_formula_positional) refers to pid_mid.o(.text.pid_formula_positional) for [Anonymous Symbol]
    pid_mid.o(.text.pid_out_limit) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    pid_mid.o(.text.pid_out_limit) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    pid_mid.o(.ARM.exidx.text.pid_out_limit) refers to pid_mid.o(.text.pid_out_limit) for [Anonymous Symbol]
    pid_mid.o(.text.pid_calculate_incremental) refers to pid_mid.o(.text.pid_formula_incremental) for pid_formula_incremental
    pid_mid.o(.text.pid_calculate_incremental) refers to pid_mid.o(.text.pid_out_limit) for pid_out_limit
    pid_mid.o(.ARM.exidx.text.pid_calculate_incremental) refers to pid_mid.o(.text.pid_calculate_incremental) for [Anonymous Symbol]
    pid_mid.o(.text.pid_formula_incremental) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    pid_mid.o(.text.pid_formula_incremental) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid_mid.o(.text.pid_formula_incremental) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    pid_mid.o(.ARM.exidx.text.pid_formula_incremental) refers to pid_mid.o(.text.pid_formula_incremental) for [Anonymous Symbol]
    pid_mid.o(.text.pid_constrain) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    pid_mid.o(.text.pid_constrain) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    pid_mid.o(.ARM.exidx.text.pid_constrain) refers to pid_mid.o(.text.pid_constrain) for [Anonymous Symbol]
    pid_mid.o(.text.pid_app_limit_integral) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    pid_mid.o(.text.pid_app_limit_integral) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    pid_mid.o(.ARM.exidx.text.pid_app_limit_integral) refers to pid_mid.o(.text.pid_app_limit_integral) for [Anonymous Symbol]
    scheduler.o(.text.SystemTime_Init) refers to scheduler.o(.bss.systemTimeMs) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.SystemTime_Init) refers to scheduler.o(.text.SystemTime_Init) for [Anonymous Symbol]
    scheduler.o(.text.SysTick_Handler) refers to scheduler.o(.bss.systemTimeMs) for [Anonymous Symbol]
    scheduler.o(.text.SysTick_Handler) refers to scheduler.o(.bss.systemTimeMs_backup) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.SysTick_Handler) refers to scheduler.o(.text.SysTick_Handler) for [Anonymous Symbol]
    scheduler.o(.text.GetTick) refers to scheduler.o(.bss.systemTimeMs) for [Anonymous Symbol]
    scheduler.o(.text.GetTick) refers to scheduler.o(.bss.systemTimeMs_backup) for [Anonymous Symbol]
    scheduler.o(.text.GetTick) refers to scheduler.o(.bss.time_error_count) for [Anonymous Symbol]
    scheduler.o(.text.GetTick) refers to scheduler.o(.bss.GetTick.last_time) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.GetTick) refers to scheduler.o(.text.GetTick) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.text.GetTick) for GetTick
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.text.delay_ms) refers to scheduler.o(.text.GetTick) for GetTick
    scheduler.o(.ARM.exidx.text.delay_ms) refers to scheduler.o(.text.delay_ms) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.delay_us) refers to scheduler.o(.text.delay_us) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to key_app.o(.text.Key_Proc) for Key_Proc
    scheduler.o(.data.scheduler_task) refers to task_func.o(.text.contorol_Task) for contorol_Task
    scheduler.o(.data.scheduler_task) refers to usart_mid.o(.text.UART1_CheckTimeout) for UART1_CheckTimeout
    scheduler.o(.data.scheduler_task) refers to usart_mid.o(.text.UART1_ProcessData) for UART1_ProcessData
    scheduler.o(.data.scheduler_task) refers to usart_mid.o(.text.UART2_CheckTimeout) for UART2_CheckTimeout
    scheduler.o(.data.scheduler_task) refers to usart_mid.o(.text.UART2_ProcessData) for UART2_ProcessData
    scheduler.o(.data.scheduler_task) refers to motor_mid.o(.text.dis_read) for dis_read
    sine_wave.o(.text.is_target_reached) refers to motor_app.o(.data.target_x) for target_x
    sine_wave.o(.text.is_target_reached) refers to motor_app.o(.bss.current_x) for current_x
    sine_wave.o(.text.is_target_reached) refers to motor_app.o(.data.target_y) for target_y
    sine_wave.o(.text.is_target_reached) refers to motor_app.o(.bss.current_y) for current_y
    sine_wave.o(.ARM.exidx.text.is_target_reached) refers to sine_wave.o(.text.is_target_reached) for [Anonymous Symbol]
    sine_wave.o(.text.common_pid_control) refers to fflti.o(.text) for __aeabi_i2f
    sine_wave.o(.text.common_pid_control) refers to pid_mid.o(.text.pid_calculate_positional) for pid_calculate_positional
    sine_wave.o(.text.common_pid_control) refers to pid_mid.o(.text.app_pid_limit_integral) for app_pid_limit_integral
    sine_wave.o(.text.common_pid_control) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    sine_wave.o(.text.common_pid_control) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    sine_wave.o(.text.common_pid_control) refers to ffixi.o(.text) for __aeabi_f2iz
    sine_wave.o(.text.common_pid_control) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.common_pid_control) refers to motor_mid.o(.text.Motor_Set_Speed) for Motor_Set_Speed
    sine_wave.o(.text.common_pid_control) refers to motor_app.o(.data.target_x) for target_x
    sine_wave.o(.text.common_pid_control) refers to motor_app.o(.bss.current_x) for current_x
    sine_wave.o(.text.common_pid_control) refers to motor_app.o(.data.target_y) for target_y
    sine_wave.o(.text.common_pid_control) refers to motor_app.o(.bss.current_y) for current_y
    sine_wave.o(.text.common_pid_control) refers to motor_app.o(.bss.pid_x) for pid_x
    sine_wave.o(.text.common_pid_control) refers to motor_app.o(.data.pid_params_x) for pid_params_x
    sine_wave.o(.text.common_pid_control) refers to motor_app.o(.bss.pid_y) for pid_y
    sine_wave.o(.text.common_pid_control) refers to motor_app.o(.data.pid_params_y) for pid_params_y
    sine_wave.o(.text.common_pid_control) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.common_pid_control) refers to sine_wave.o(.text.common_pid_control) for [Anonymous Symbol]
    sine_wave.o(.text.common_motion_stop) refers to sine_wave.o(.text.laser_control) for laser_control
    sine_wave.o(.text.common_motion_stop) refers to motor_mid.o(.text.motor_stop) for motor_stop
    sine_wave.o(.text.common_motion_stop) refers to sine_wave.o(.bss.sine_wave_mode) for sine_wave_mode
    sine_wave.o(.text.common_motion_stop) refers to sine_wave.o(.bss.triangle_mode) for triangle_mode
    sine_wave.o(.text.common_motion_stop) refers to sine_wave.o(.bss.triangle_point_index) for triangle_point_index
    sine_wave.o(.ARM.exidx.text.common_motion_stop) refers to sine_wave.o(.text.common_motion_stop) for [Anonymous Symbol]
    sine_wave.o(.text.laser_control) refers to sine_wave.o(.bss.laser_state) for laser_state
    sine_wave.o(.ARM.exidx.text.laser_control) refers to sine_wave.o(.text.laser_control) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_init) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.sine_wave_init) refers to sine_wave.o(.bss.sine_wave_mode) for sine_wave_mode
    sine_wave.o(.text.sine_wave_init) refers to sine_wave.o(.bss.sine_points_count) for sine_points_count
    sine_wave.o(.text.sine_wave_init) refers to sine_wave.o(.bss.sine_direction) for sine_direction
    sine_wave.o(.text.sine_wave_init) refers to sine_wave.o(.bss.laser_state) for laser_state
    sine_wave.o(.text.sine_wave_init) refers to sine_wave.o(.bss.triangle_mode) for triangle_mode
    sine_wave.o(.text.sine_wave_init) refers to sine_wave.o(.bss.triangle_point_index) for triangle_point_index
    sine_wave.o(.text.sine_wave_init) refers to sine_wave.o(.data.motion_mode) for motion_mode
    sine_wave.o(.text.sine_wave_init) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.sine_wave_init) refers to sine_wave.o(.text.sine_wave_init) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_set_params) refers to f2d.o(.text) for __aeabi_f2d
    sine_wave.o(.text.sine_wave_set_params) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.sine_wave_set_params) refers to sine_wave.o(.data.sine_params) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_set_params) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.sine_wave_set_params) refers to sine_wave.o(.text.sine_wave_set_params) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_set_center) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.sine_wave_set_center) refers to sine_wave.o(.data.sine_params) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_set_center) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.sine_wave_set_center) refers to sine_wave.o(.text.sine_wave_set_center) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_calculate_point) refers to fflti.o(.text) for __aeabi_ui2f
    sine_wave.o(.text.sine_wave_calculate_point) refers to fdiv.o(.text) for __aeabi_fdiv
    sine_wave.o(.text.sine_wave_calculate_point) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    sine_wave.o(.text.sine_wave_calculate_point) refers to ffixi.o(.text) for __aeabi_f2iz
    sine_wave.o(.text.sine_wave_calculate_point) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    sine_wave.o(.text.sine_wave_calculate_point) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    sine_wave.o(.text.sine_wave_calculate_point) refers to sinf.o(i.sinf) for sinf
    sine_wave.o(.text.sine_wave_calculate_point) refers to sine_wave.o(.data.sine_params) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_calculate_point) refers to sine_wave.o(.bss.sine_direction) for sine_direction
    sine_wave.o(.text.sine_wave_calculate_point) refers to motor_app.o(.bss.xy) for xy
    sine_wave.o(.ARM.exidx.text.sine_wave_calculate_point) refers to sine_wave.o(.text.sine_wave_calculate_point) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_draw) refers to sine_wave.o(.text.sine_wave_set_params) for sine_wave_set_params
    sine_wave.o(.text.sine_wave_draw) refers to sine_wave.o(.text.sine_wave_set_center) for sine_wave_set_center
    sine_wave.o(.text.sine_wave_draw) refers to sine_wave.o(.text.laser_control) for laser_control
    sine_wave.o(.text.sine_wave_draw) refers to sine_wave.o(.text.sine_wave_calculate_point) for sine_wave_calculate_point
    sine_wave.o(.text.sine_wave_draw) refers to motor_app.o(.text.pid_target) for pid_target
    sine_wave.o(.text.sine_wave_draw) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.sine_wave_draw) refers to motor_app.o(.bss.current_x) for current_x
    sine_wave.o(.text.sine_wave_draw) refers to motor_app.o(.bss.current_y) for current_y
    sine_wave.o(.text.sine_wave_draw) refers to sine_wave.o(.bss.sine_wave_mode) for sine_wave_mode
    sine_wave.o(.text.sine_wave_draw) refers to sine_wave.o(.bss.sine_points_count) for sine_points_count
    sine_wave.o(.text.sine_wave_draw) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.sine_wave_draw) refers to sine_wave.o(.text.sine_wave_draw) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_task) refers to sine_wave.o(.text.is_target_reached) for is_target_reached
    sine_wave.o(.text.sine_wave_task) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.sine_wave_task) refers to sine_wave.o(.text.sine_wave_calculate_point) for sine_wave_calculate_point
    sine_wave.o(.text.sine_wave_task) refers to motor_app.o(.text.pid_target) for pid_target
    sine_wave.o(.text.sine_wave_task) refers to sine_wave.o(.text.sine_wave_pid_control) for sine_wave_pid_control
    sine_wave.o(.text.sine_wave_task) refers to sine_wave.o(.bss.sine_wave_mode) for sine_wave_mode
    sine_wave.o(.text.sine_wave_task) refers to sine_wave.o(.bss.sine_points_count) for sine_points_count
    sine_wave.o(.text.sine_wave_task) refers to sine_wave.o(.data.sine_params) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_task) refers to sine_wave.o(.bss.sine_direction) for sine_direction
    sine_wave.o(.text.sine_wave_task) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.sine_wave_task) refers to sine_wave.o(.text.sine_wave_task) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_pid_control) refers to sine_wave.o(.text.common_pid_control) for common_pid_control
    sine_wave.o(.text.sine_wave_pid_control) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.sine_wave_pid_control) refers to sine_wave.o(.text.sine_wave_pid_control) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_stop) refers to sine_wave.o(.text.common_motion_stop) for common_motion_stop
    sine_wave.o(.text.sine_wave_stop) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.sine_wave_stop) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.sine_wave_stop) refers to sine_wave.o(.text.sine_wave_stop) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_preset_1) refers to fflti.o(.text) for __aeabi_i2f
    sine_wave.o(.text.sine_wave_preset_1) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    sine_wave.o(.text.sine_wave_preset_1) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.sine_wave_preset_1) refers to sine_wave.o(.text.sine_wave_calculate_point) for sine_wave_calculate_point
    sine_wave.o(.text.sine_wave_preset_1) refers to motor_app.o(.text.pid_target) for pid_target
    sine_wave.o(.text.sine_wave_preset_1) refers to motor_app.o(.bss.xy) for xy
    sine_wave.o(.text.sine_wave_preset_1) refers to sine_wave.o(.data.sine_params) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_preset_1) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.text.sine_wave_preset_1) refers to sine_wave.o(.bss.sine_wave_mode) for sine_wave_mode
    sine_wave.o(.text.sine_wave_preset_1) refers to sine_wave.o(.bss.sine_points_count) for sine_points_count
    sine_wave.o(.text.sine_wave_preset_1) refers to sine_wave.o(.bss.sine_direction) for sine_direction
    sine_wave.o(.ARM.exidx.text.sine_wave_preset_1) refers to sine_wave.o(.text.sine_wave_preset_1) for [Anonymous Symbol]
    sine_wave.o(.text.triangle_set_points) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.triangle_set_points) refers to motor_app.o(.bss.xy) for xy
    sine_wave.o(.text.triangle_set_points) refers to sine_wave.o(.bss.triangle_points) for triangle_points
    sine_wave.o(.text.triangle_set_points) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.triangle_set_points) refers to sine_wave.o(.text.triangle_set_points) for [Anonymous Symbol]
    sine_wave.o(.text.triangle_start) refers to motor_app.o(.text.pid_target) for pid_target
    sine_wave.o(.text.triangle_start) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.triangle_start) refers to sine_wave.o(.bss.triangle_mode) for triangle_mode
    sine_wave.o(.text.triangle_start) refers to sine_wave.o(.bss.triangle_point_index) for triangle_point_index
    sine_wave.o(.text.triangle_start) refers to sine_wave.o(.bss.triangle_points) for triangle_points
    sine_wave.o(.text.triangle_start) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.triangle_start) refers to sine_wave.o(.text.triangle_start) for [Anonymous Symbol]
    sine_wave.o(.text.triangle_stop) refers to sine_wave.o(.text.common_motion_stop) for common_motion_stop
    sine_wave.o(.text.triangle_stop) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.triangle_stop) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.triangle_stop) refers to sine_wave.o(.text.triangle_stop) for [Anonymous Symbol]
    sine_wave.o(.text.triangle_task) refers to sine_wave.o(.text.is_target_reached) for is_target_reached
    sine_wave.o(.text.triangle_task) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.triangle_task) refers to motor_app.o(.text.pid_target) for pid_target
    sine_wave.o(.text.triangle_task) refers to sine_wave.o(.text.triangle_pid_control) for triangle_pid_control
    sine_wave.o(.text.triangle_task) refers to sine_wave.o(.bss.triangle_mode) for triangle_mode
    sine_wave.o(.text.triangle_task) refers to sine_wave.o(.bss.triangle_point_index) for triangle_point_index
    sine_wave.o(.text.triangle_task) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.text.triangle_task) refers to sine_wave.o(.bss.triangle_points) for triangle_points
    sine_wave.o(.ARM.exidx.text.triangle_task) refers to sine_wave.o(.text.triangle_task) for [Anonymous Symbol]
    sine_wave.o(.text.triangle_pid_control) refers to sine_wave.o(.text.common_pid_control) for common_pid_control
    sine_wave.o(.text.triangle_pid_control) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.triangle_pid_control) refers to sine_wave.o(.text.triangle_pid_control) for [Anonymous Symbol]
    sine_wave.o(.text.unified_motion_task) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.unified_motion_task) refers to sine_wave.o(.text.triangle_task) for triangle_task
    sine_wave.o(.text.unified_motion_task) refers to sine_wave.o(.text.sine_wave_task) for sine_wave_task
    sine_wave.o(.text.unified_motion_task) refers to sine_wave.o(.bss.unified_motion_task.debug_count) for [Anonymous Symbol]
    sine_wave.o(.text.unified_motion_task) refers to sine_wave.o(.data.motion_mode) for motion_mode
    sine_wave.o(.text.unified_motion_task) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.unified_motion_task) refers to sine_wave.o(.text.unified_motion_task) for [Anonymous Symbol]
    sine_wave.o(.text.set_motion_mode) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.set_motion_mode) refers to sine_wave.o(.data.motion_mode) for motion_mode
    sine_wave.o(.text.set_motion_mode) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.set_motion_mode) refers to sine_wave.o(.text.set_motion_mode) for [Anonymous Symbol]
    sine_wave.o(.text.start_motion) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.start_motion) refers to sine_wave.o(.text.triangle_set_points) for triangle_set_points
    sine_wave.o(.text.start_motion) refers to sine_wave.o(.text.triangle_start) for triangle_start
    sine_wave.o(.text.start_motion) refers to sine_wave.o(.text.sine_wave_preset_1) for sine_wave_preset_1
    sine_wave.o(.text.start_motion) refers to sine_wave.o(.data.motion_mode) for motion_mode
    sine_wave.o(.text.start_motion) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.start_motion) refers to sine_wave.o(.text.start_motion) for [Anonymous Symbol]
    sine_wave.o(.text.stop_motion) refers to sine_wave.o(.text.common_motion_stop) for common_motion_stop
    sine_wave.o(.text.stop_motion) refers to usart_mid.o(.text.uart_printf) for uart_printf
    sine_wave.o(.text.stop_motion) refers to sine_wave.o(.rodata.str1.1) for [Anonymous Symbol]
    sine_wave.o(.ARM.exidx.text.stop_motion) refers to sine_wave.o(.text.stop_motion) for [Anonymous Symbol]
    task_func.o(.text.contorol_Task) refers to motor_app.o(.text.app_pid_calc) for app_pid_calc
    task_func.o(.text.contorol_Task) refers to sine_wave.o(.text.unified_motion_task) for unified_motion_task
    task_func.o(.text.contorol_Task) refers to task_func.o(.bss.open) for open
    task_func.o(.text.contorol_Task) refers to task_func.o(.bss.control_mode) for control_mode
    task_func.o(.ARM.exidx.text.contorol_Task) refers to task_func.o(.text.contorol_Task) for [Anonymous Symbol]
    task_func.o(.text.state_up) refers to motor_app.o(.text.pid_target) for pid_target
    task_func.o(.text.state_up) refers to task_func.o(.data.state_data) for state_data
    task_func.o(.text.state_up) refers to motor_app.o(.bss.xy) for xy
    task_func.o(.text.state_up) refers to task_func.o(.bss.open) for open
    task_func.o(.text.state_up) refers to key_app.o(.bss.xy_z) for xy_z
    task_func.o(.ARM.exidx.text.state_up) refers to task_func.o(.text.state_up) for [Anonymous Symbol]
    usart_mid.o(.text.uart_send_char) refers to usart_mid.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    usart_mid.o(.text.uart_send_char) refers to usart_mid.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    usart_mid.o(.ARM.exidx.text.uart_send_char) refers to usart_mid.o(.text.uart_send_char) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_UART_isBusy) refers to usart_mid.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_UART_transmitData) refers to usart_mid.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    usart_mid.o(.text.uart_send_string) refers to usart_mid.o(.text.uart_send_char) for uart_send_char
    usart_mid.o(.ARM.exidx.text.uart_send_string) refers to usart_mid.o(.text.uart_send_string) for [Anonymous Symbol]
    usart_mid.o(.text.uart_send_data) refers to usart_mid.o(.text.uart_send_char) for uart_send_char
    usart_mid.o(.ARM.exidx.text.uart_send_data) refers to usart_mid.o(.text.uart_send_data) for [Anonymous Symbol]
    usart_mid.o(.text.fputc) refers to usart_mid.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    usart_mid.o(.text.fputc) refers to usart_mid.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    usart_mid.o(.ARM.exidx.text.fputc) refers to usart_mid.o(.text.fputc) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.my_strlen) refers to usart_mid.o(.text.my_strlen) for [Anonymous Symbol]
    usart_mid.o(.text.int_to_string) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    usart_mid.o(.text.int_to_string) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    usart_mid.o(.ARM.exidx.text.int_to_string) refers to usart_mid.o(.text.int_to_string) for [Anonymous Symbol]
    usart_mid.o(.text.uint_to_hex_string) refers to usart_mid.o(.rodata.str1.1) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.uint_to_hex_string) refers to usart_mid.o(.text.uint_to_hex_string) for [Anonymous Symbol]
    usart_mid.o(.text.float_to_string) refers to ffixi.o(.text) for __aeabi_f2iz
    usart_mid.o(.text.float_to_string) refers to fflti.o(.text) for __aeabi_i2f
    usart_mid.o(.text.float_to_string) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    usart_mid.o(.text.float_to_string) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    usart_mid.o(.text.float_to_string) refers to usart_mid.o(.text.int_to_string) for int_to_string
    usart_mid.o(.text.float_to_string) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    usart_mid.o(.ARM.exidx.text.float_to_string) refers to usart_mid.o(.text.float_to_string) for [Anonymous Symbol]
    usart_mid.o(.text.uart_printf) refers to usart_mid.o(.text.int_to_string) for int_to_string
    usart_mid.o(.text.uart_printf) refers to usart_mid.o(.text.uart_send_string) for uart_send_string
    usart_mid.o(.text.uart_printf) refers to usart_mid.o(.text.my_strlen) for my_strlen
    usart_mid.o(.text.uart_printf) refers to usart_mid.o(.text.uint_to_hex_string) for uint_to_hex_string
    usart_mid.o(.text.uart_printf) refers to usart_mid.o(.text.uart_send_char) for uart_send_char
    usart_mid.o(.text.uart_printf) refers to d2f.o(.text) for __aeabi_d2f
    usart_mid.o(.text.uart_printf) refers to usart_mid.o(.text.float_to_string) for float_to_string
    usart_mid.o(.ARM.exidx.text.uart_printf) refers to usart_mid.o(.text.uart_printf) for [Anonymous Symbol]
    usart_mid.o(.text.send_hex_byte) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    usart_mid.o(.text.send_hex_byte) refers to usart_mid.o(.text.uart_send_char) for uart_send_char
    usart_mid.o(.text.send_hex_byte) refers to usart_mid.o(.rodata.str1.1) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.send_hex_byte) refers to usart_mid.o(.text.send_hex_byte) for [Anonymous Symbol]
    usart_mid.o(.text.UART_DMA_Init) refers to usart_mid.o(.text.DL_DMA_setSrcAddr) for DL_DMA_setSrcAddr
    usart_mid.o(.text.UART_DMA_Init) refers to usart_mid.o(.text.DL_DMA_setDestAddr) for DL_DMA_setDestAddr
    usart_mid.o(.text.UART_DMA_Init) refers to usart_mid.o(.text.DL_DMA_setTransferSize) for DL_DMA_setTransferSize
    usart_mid.o(.text.UART_DMA_Init) refers to usart_mid.o(.text.DL_DMA_enableChannel) for DL_DMA_enableChannel
    usart_mid.o(.text.UART_DMA_Init) refers to usart_mid.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    usart_mid.o(.text.UART_DMA_Init) refers to usart_mid.o(.text.UART1_Timeout_Init) for UART1_Timeout_Init
    usart_mid.o(.text.UART_DMA_Init) refers to usart_mid.o(.text.UART2_Timeout_Init) for UART2_Timeout_Init
    usart_mid.o(.text.UART_DMA_Init) refers to usart_mid.o(.bss.gUartDma) for gUartDma
    usart_mid.o(.ARM.exidx.text.UART_DMA_Init) refers to usart_mid.o(.text.UART_DMA_Init) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_DMA_setSrcAddr) refers to usart_mid.o(.text.DL_DMA_setSrcAddr) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_DMA_setDestAddr) refers to usart_mid.o(.text.DL_DMA_setDestAddr) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_DMA_setTransferSize) refers to usart_mid.o(.text.DL_DMA_setTransferSize) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_DMA_enableChannel) refers to usart_mid.o(.text.DL_DMA_enableChannel) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to usart_mid.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    usart_mid.o(.text.UART1_Timeout_Init) refers to usart_mid.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    usart_mid.o(.text.UART1_Timeout_Init) refers to usart_mid.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    usart_mid.o(.text.UART1_Timeout_Init) refers to usart_mid.o(.bss.gUart1Handle) for gUart1Handle
    usart_mid.o(.ARM.exidx.text.UART1_Timeout_Init) refers to usart_mid.o(.text.UART1_Timeout_Init) for [Anonymous Symbol]
    usart_mid.o(.text.UART2_Timeout_Init) refers to usart_mid.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    usart_mid.o(.text.UART2_Timeout_Init) refers to usart_mid.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    usart_mid.o(.text.UART2_Timeout_Init) refers to usart_mid.o(.bss.gUart2Handle) for gUart2Handle
    usart_mid.o(.ARM.exidx.text.UART2_Timeout_Init) refers to usart_mid.o(.text.UART2_Timeout_Init) for [Anonymous Symbol]
    usart_mid.o(.text.DMA_REST) refers to usart_mid.o(.text.DL_DMA_disableChannel) for DL_DMA_disableChannel
    usart_mid.o(.text.DMA_REST) refers to usart_mid.o(.text.DL_DMA_setDestAddr) for DL_DMA_setDestAddr
    usart_mid.o(.text.DMA_REST) refers to usart_mid.o(.text.DL_DMA_setTransferSize) for DL_DMA_setTransferSize
    usart_mid.o(.text.DMA_REST) refers to usart_mid.o(.text.DL_DMA_enableChannel) for DL_DMA_enableChannel
    usart_mid.o(.text.DMA_REST) refers to usart_mid.o(.bss.gUartDma) for gUartDma
    usart_mid.o(.ARM.exidx.text.DMA_REST) refers to usart_mid.o(.text.DMA_REST) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_DMA_disableChannel) refers to usart_mid.o(.text.DL_DMA_disableChannel) for [Anonymous Symbol]
    usart_mid.o(.text.Get_CRC16) refers to usart_mid.o(.rodata.CRC16_table) for CRC16_table
    usart_mid.o(.ARM.exidx.text.Get_CRC16) refers to usart_mid.o(.text.Get_CRC16) for [Anonymous Symbol]
    usart_mid.o(.text.uint_to_float) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    usart_mid.o(.text.uint_to_float) refers to fflti.o(.text) for __aeabi_i2f
    usart_mid.o(.text.uint_to_float) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    usart_mid.o(.text.uint_to_float) refers to fdiv.o(.text) for __aeabi_fdiv
    usart_mid.o(.text.uint_to_float) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    usart_mid.o(.ARM.exidx.text.uint_to_float) refers to usart_mid.o(.text.uint_to_float) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.parse_imu_frame) refers to usart_mid.o(.text.parse_imu_frame) for [Anonymous Symbol]
    usart_mid.o(.text.find_frame) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    usart_mid.o(.ARM.exidx.text.find_frame) refers to usart_mid.o(.text.find_frame) for [Anonymous Symbol]
    usart_mid.o(.text.ProcessReceivedData) refers to usart_mid.o(.text.DL_DMA_disableChannel) for DL_DMA_disableChannel
    usart_mid.o(.text.ProcessReceivedData) refers to usart_mid.o(.text.DL_DMA_setDestAddr) for DL_DMA_setDestAddr
    usart_mid.o(.text.ProcessReceivedData) refers to usart_mid.o(.text.DL_DMA_setTransferSize) for DL_DMA_setTransferSize
    usart_mid.o(.text.ProcessReceivedData) refers to usart_mid.o(.text.DL_DMA_enableChannel) for DL_DMA_enableChannel
    usart_mid.o(.text.ProcessReceivedData) refers to usart_mid.o(.bss.gCheckUART) for gCheckUART
    usart_mid.o(.text.ProcessReceivedData) refers to usart_mid.o(.bss.gUartDma) for gUartDma
    usart_mid.o(.ARM.exidx.text.ProcessReceivedData) refers to usart_mid.o(.text.ProcessReceivedData) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to usart_mid.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    usart_mid.o(.text.UART1_CheckTimeout) refers to scheduler.o(.text.GetTick) for GetTick
    usart_mid.o(.text.UART1_CheckTimeout) refers to usart_mid.o(.bss.gUart1Handle) for gUart1Handle
    usart_mid.o(.text.UART1_CheckTimeout) refers to usart_mid.o(.bss.rectangle_parsed) for rectangle_parsed
    usart_mid.o(.ARM.exidx.text.UART1_CheckTimeout) refers to usart_mid.o(.text.UART1_CheckTimeout) for [Anonymous Symbol]
    usart_mid.o(.text.angleToDistance) refers to ddiv.o(.text) for __aeabi_ddiv
    usart_mid.o(.text.angleToDistance) refers to daddsub.o(.text) for __aeabi_dadd
    usart_mid.o(.text.angleToDistance) refers to dmul.o(.text) for __aeabi_dmul
    usart_mid.o(.ARM.exidx.text.angleToDistance) refers to usart_mid.o(.text.angleToDistance) for [Anonymous Symbol]
    usart_mid.o(.text.UART1_ProcessData) refers to usart_mid.o(.text.motor_packet_sliding_parse) for motor_packet_sliding_parse
    usart_mid.o(.text.UART1_ProcessData) refers to usart_mid.o(.bss.gUart1Handle) for gUart1Handle
    usart_mid.o(.text.UART1_ProcessData) refers to usart_mid.o(.bss.uart1_data_count) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.UART1_ProcessData) refers to usart_mid.o(.text.UART1_ProcessData) for [Anonymous Symbol]
    usart_mid.o(.text.motor_packet_sliding_parse) refers to fflti.o(.text) for __aeabi_ui2f
    usart_mid.o(.text.motor_packet_sliding_parse) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    usart_mid.o(.text.motor_packet_sliding_parse) refers to usart_mid.o(.bss.motor_packet_sliding_parse.rxCounter) for [Anonymous Symbol]
    usart_mid.o(.text.motor_packet_sliding_parse) refers to usart_mid.o(.bss.motor_packet_sliding_parse.rxBuffer) for [Anonymous Symbol]
    usart_mid.o(.text.motor_packet_sliding_parse) refers to usart_mid.o(.bss.Motor_Cur_Pos) for Motor_Cur_Pos
    usart_mid.o(.ARM.exidx.text.motor_packet_sliding_parse) refers to usart_mid.o(.text.motor_packet_sliding_parse) for [Anonymous Symbol]
    usart_mid.o(.text.speed_cal_read) refers to usart_mid.o(.bss.Motor_Speed_RPM) for Motor_Speed_RPM
    usart_mid.o(.ARM.exidx.text.speed_cal_read) refers to usart_mid.o(.text.speed_cal_read) for [Anonymous Symbol]
    usart_mid.o(.text.UART2_CheckTimeout) refers to scheduler.o(.text.GetTick) for GetTick
    usart_mid.o(.text.UART2_CheckTimeout) refers to usart_mid.o(.bss.gUart2Handle) for gUart2Handle
    usart_mid.o(.ARM.exidx.text.UART2_CheckTimeout) refers to usart_mid.o(.text.UART2_CheckTimeout) for [Anonymous Symbol]
    usart_mid.o(.text.UART2_ProcessData) refers to usart_mid.o(.text.vision_packet_sliding_parse) for vision_packet_sliding_parse
    usart_mid.o(.text.UART2_ProcessData) refers to usart_mid.o(.bss.gUart2Handle) for gUart2Handle
    usart_mid.o(.ARM.exidx.text.UART2_ProcessData) refers to usart_mid.o(.text.UART2_ProcessData) for [Anonymous Symbol]
    usart_mid.o(.text.vision_packet_sliding_parse) refers to motor_app.o(.text.get_data) for get_data
    usart_mid.o(.text.vision_packet_sliding_parse) refers to motor_app.o(.text.read_place) for read_place
    usart_mid.o(.text.vision_packet_sliding_parse) refers to usart_mid.o(.bss.vision_packet_sliding_parse.rxCounter) for [Anonymous Symbol]
    usart_mid.o(.text.vision_packet_sliding_parse) refers to usart_mid.o(.bss.vision_packet_sliding_parse.rxBuffer) for [Anonymous Symbol]
    usart_mid.o(.text.vision_packet_sliding_parse) refers to usart_mid.o(.bss.rectangle_parsed) for rectangle_parsed
    usart_mid.o(.ARM.exidx.text.vision_packet_sliding_parse) refers to usart_mid.o(.text.vision_packet_sliding_parse) for [Anonymous Symbol]
    usart_mid.o(.text.UART0_IRQHandler) refers to usart_mid.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    usart_mid.o(.text.UART0_IRQHandler) refers to usart_mid.o(.bss.gCheckUART) for gCheckUART
    usart_mid.o(.ARM.exidx.text.UART0_IRQHandler) refers to usart_mid.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to usart_mid.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    usart_mid.o(.text.UART1_IRQHandler) refers to usart_mid.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    usart_mid.o(.text.UART1_IRQHandler) refers to usart_mid.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    usart_mid.o(.text.UART1_IRQHandler) refers to scheduler.o(.text.GetTick) for GetTick
    usart_mid.o(.text.UART1_IRQHandler) refers to usart_mid.o(.bss.uart_data) for uart_data
    usart_mid.o(.text.UART1_IRQHandler) refers to usart_mid.o(.bss.gUart1Handle) for gUart1Handle
    usart_mid.o(.ARM.exidx.text.UART1_IRQHandler) refers to usart_mid.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    usart_mid.o(.ARM.exidx.text.DL_UART_receiveData) refers to usart_mid.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    usart_mid.o(.text.UART3_IRQHandler) refers to usart_mid.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    usart_mid.o(.text.UART3_IRQHandler) refers to usart_mid.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    usart_mid.o(.text.UART3_IRQHandler) refers to scheduler.o(.text.GetTick) for GetTick
    usart_mid.o(.text.UART3_IRQHandler) refers to usart_mid.o(.bss.uart_data) for uart_data
    usart_mid.o(.text.UART3_IRQHandler) refers to usart_mid.o(.bss.gUart2Handle) for gUart2Handle
    usart_mid.o(.ARM.exidx.text.UART3_IRQHandler) refers to usart_mid.o(.text.UART3_IRQHandler) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata.cst32) for .L__const.DL_MCAN_getMsgObjSize.objSize
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata.cst32) for .L__const.DL_MCAN_getMsgObjSize.objSize
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i.__eqsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__eqsf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i.__gesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gesf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__gtsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gtsf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__lesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__lesf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__ltsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__ltsf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__nesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__nesf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fdiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(.text) refers to fdiv.o(.constdata) for .constdata
    fdiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixui.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.sinf) for sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    sinf.o(i.sinf) refers to frnd.o(.text) for _frnd
    sinf.o(i.sinf) refers to ffixi.o(.text) for __aeabi_f2iz
    sinf.o(i.sinf) refers to faddsub.o(x$fpl$frsb) for __aeabi_frsub
    sinf.o(i.sinf) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    sinf.o(i.sinf) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    sinf.o(i.sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    frnd.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(.text) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to fscalbn.o(.text) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(.text) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to fscalbn.o(.text) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to fscalbn.o(.text) for __ARM_scalbnf
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    rredf.o(i.__mathlib_rredf2) refers to fflti.o(.text) for __aeabi_i2f
    rredf.o(i.__mathlib_rredf2) refers to fscalbn.o(.text) for __ARM_scalbnf
    rredf.o(i.__mathlib_rredf2) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    rredf.o(i.__mathlib_rredf2) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    rredf.o(i.__mathlib_rredf2) refers to faddsub.o(x$fpl$frsb) for __aeabi_frsub
    rredf.o(i.__mathlib_rredf2) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    fscalbn.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    rredf.o(i.__ARM_common_ll_muluu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MCAN0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (76 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (76 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFPCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setMFPCLKSource), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH2_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SysTick_Config), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_enableModuleClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_MCAN_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing mydefine.o(.text), (0 bytes).
    Removing key_app.o(.text), (0 bytes).
    Removing key_app.o(.ARM.exidx.text.Key_Read), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.example_pixel_coordinate_control), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.Key_Proc), (8 bytes).
    Removing key_app.o(.bss.count_Tt), (1 bytes).
    Removing key_app.o(.bss.angle_error), (4 bytes).
    Removing key_app.o(.bss.reference_yaw), (4 bytes).
    Removing key_app.o(.bss.count_return), (4 bytes).
    Removing key_app.o(.bss.began_time), (4 bytes).
    Removing key_app.o(.bss.Key_Count), (1 bytes).
    Removing motor_app.o(.text), (0 bytes).
    Removing motor_app.o(.ARM.exidx.text.app_pid_init), (8 bytes).
    Removing motor_app.o(.text.app_pid_set_x_params), (92 bytes).
    Removing motor_app.o(.ARM.exidx.text.app_pid_set_x_params), (8 bytes).
    Removing motor_app.o(.text.app_pid_set_y_params), (92 bytes).
    Removing motor_app.o(.ARM.exidx.text.app_pid_set_y_params), (8 bytes).
    Removing motor_app.o(.ARM.exidx.text.read_place), (8 bytes).
    Removing motor_app.o(.ARM.exidx.text.pid_target), (8 bytes).
    Removing motor_app.o(.ARM.exidx.text.app_pid_calc), (8 bytes).
    Removing motor_app.o(.text.Read_L), (36 bytes).
    Removing motor_app.o(.ARM.exidx.text.Read_L), (8 bytes).
    Removing motor_app.o(.ARM.exidx.text.get_data), (8 bytes).
    Removing motor_app.o(.bss.origin_x), (4 bytes).
    Removing motor_app.o(.bss.origin_y), (4 bytes).
    Removing motor_mid.o(.text), (0 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_init), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_stop), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_distance), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_speed), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_readdistance), (8 bytes).
    Removing motor_mid.o(.text.motor_readspeed), (36 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_readspeed), (8 bytes).
    Removing motor_mid.o(.text.motor_clear_position), (40 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_clear_position), (8 bytes).
    Removing motor_mid.o(.text.motor_clear_all_position), (16 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_clear_all_position), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_sync), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.Motor_Set_Speed), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.dis_read), (8 bytes).
    Removing motor_mid.o(.text.angle_to_pixel_x), (100 bytes).
    Removing motor_mid.o(.ARM.exidx.text.angle_to_pixel_x), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.constrain_angle), (8 bytes).
    Removing motor_mid.o(.text.angle_to_pixel_y), (96 bytes).
    Removing motor_mid.o(.ARM.exidx.text.angle_to_pixel_y), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.pixel_to_angle_x), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.pixel_to_angle_y), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.move_to_pixel), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.motor_move_angle), (8 bytes).
    Removing motor_mid.o(.text.gimbal_set_config), (72 bytes).
    Removing motor_mid.o(.ARM.exidx.text.gimbal_set_config), (8 bytes).
    Removing motor_mid.o(.text.gimbal_get_config), (8 bytes).
    Removing motor_mid.o(.ARM.exidx.text.gimbal_get_config), (8 bytes).
    Removing motor_mid.o(.text.gimbal_set_center), (48 bytes).
    Removing motor_mid.o(.ARM.exidx.text.gimbal_set_center), (8 bytes).
    Removing motor_mid.o(.text.gimbal_move_to_angle), (28 bytes).
    Removing motor_mid.o(.ARM.exidx.text.gimbal_move_to_angle), (8 bytes).
    Removing motor_mid.o(.text.gimbal_move_relative), (28 bytes).
    Removing motor_mid.o(.ARM.exidx.text.gimbal_move_relative), (8 bytes).
    Removing motor_mid.o(.text.gimbal_test_conversion), (376 bytes).
    Removing motor_mid.o(.ARM.exidx.text.gimbal_test_conversion), (8 bytes).
    Removing motor_mid.o(.data.READ_SPEED), (3 bytes).
    Removing motor_mid.o(.data.CLEAR), (4 bytes).
    Removing motor_mid.o(.bss.cmd), (16 bytes).
    Removing motor_mid.o(.data.gimbal_config), (32 bytes).
    Removing motor_mid.o(.rodata.str1.1), (261 bytes).
    Removing motor_mid.o(.rodata..L__const.gimbal_test_conversion.test_angles), (20 bytes).
    Removing motor_mid.o(.rodata..L__const.gimbal_test_conversion.test_pixels_x), (20 bytes).
    Removing motor_mid.o(.rodata..L__const.gimbal_test_conversion.test_pixels_y), (20 bytes).
    Removing pid_mid.o(.text), (0 bytes).
    Removing pid_mid.o(.ARM.exidx.text.app_pid_limit_integral), (8 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_init), (8 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_set_target), (8 bytes).
    Removing pid_mid.o(.text.pid_set_params), (34 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_set_params), (8 bytes).
    Removing pid_mid.o(.text.pid_set_limit), (16 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_set_limit), (8 bytes).
    Removing pid_mid.o(.text.pid_reset), (38 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_reset), (8 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_calculate_positional), (8 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_formula_positional), (8 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_out_limit), (8 bytes).
    Removing pid_mid.o(.text.pid_calculate_incremental), (34 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_calculate_incremental), (8 bytes).
    Removing pid_mid.o(.text.pid_formula_incremental), (164 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_formula_incremental), (8 bytes).
    Removing pid_mid.o(.text.pid_constrain), (62 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_constrain), (8 bytes).
    Removing pid_mid.o(.text.pid_app_limit_integral), (64 bytes).
    Removing pid_mid.o(.ARM.exidx.text.pid_app_limit_integral), (8 bytes).
    Removing pid_mid.o(.bss.countXXXX), (4 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.SystemTime_Init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.GetTick), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing scheduler.o(.text.delay_us), (38 bytes).
    Removing scheduler.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing sine_wave.o(.text), (0 bytes).
    Removing sine_wave.o(.ARM.exidx.text.is_target_reached), (8 bytes).
    Removing sine_wave.o(.ARM.exidx.text.common_pid_control), (8 bytes).
    Removing sine_wave.o(.text.common_motion_stop), (40 bytes).
    Removing sine_wave.o(.ARM.exidx.text.common_motion_stop), (8 bytes).
    Removing sine_wave.o(.text.laser_control), (20 bytes).
    Removing sine_wave.o(.ARM.exidx.text.laser_control), (8 bytes).
    Removing sine_wave.o(.text.sine_wave_init), (84 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_init), (8 bytes).
    Removing sine_wave.o(.text.sine_wave_set_params), (108 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_set_params), (8 bytes).
    Removing sine_wave.o(.text.sine_wave_set_center), (52 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_set_center), (8 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_calculate_point), (8 bytes).
    Removing sine_wave.o(.text.sine_wave_draw), (132 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_draw), (8 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_task), (8 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_pid_control), (8 bytes).
    Removing sine_wave.o(.text.sine_wave_stop), (24 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_stop), (8 bytes).
    Removing sine_wave.o(.text.sine_wave_preset_1), (340 bytes).
    Removing sine_wave.o(.ARM.exidx.text.sine_wave_preset_1), (8 bytes).
    Removing sine_wave.o(.text.triangle_set_points), (104 bytes).
    Removing sine_wave.o(.ARM.exidx.text.triangle_set_points), (8 bytes).
    Removing sine_wave.o(.text.triangle_start), (76 bytes).
    Removing sine_wave.o(.ARM.exidx.text.triangle_start), (8 bytes).
    Removing sine_wave.o(.text.triangle_stop), (24 bytes).
    Removing sine_wave.o(.ARM.exidx.text.triangle_stop), (8 bytes).
    Removing sine_wave.o(.ARM.exidx.text.triangle_task), (8 bytes).
    Removing sine_wave.o(.ARM.exidx.text.triangle_pid_control), (8 bytes).
    Removing sine_wave.o(.ARM.exidx.text.unified_motion_task), (8 bytes).
    Removing sine_wave.o(.text.set_motion_mode), (76 bytes).
    Removing sine_wave.o(.ARM.exidx.text.set_motion_mode), (8 bytes).
    Removing sine_wave.o(.text.start_motion), (116 bytes).
    Removing sine_wave.o(.ARM.exidx.text.start_motion), (8 bytes).
    Removing sine_wave.o(.text.stop_motion), (24 bytes).
    Removing sine_wave.o(.ARM.exidx.text.stop_motion), (8 bytes).
    Removing sine_wave.o(.bss.laser_state), (1 bytes).
    Removing task_func.o(.text), (0 bytes).
    Removing task_func.o(.ARM.exidx.text.contorol_Task), (8 bytes).
    Removing task_func.o(.text.state_up), (92 bytes).
    Removing task_func.o(.ARM.exidx.text.state_up), (8 bytes).
    Removing usart_mid.o(.text), (0 bytes).
    Removing usart_mid.o(.ARM.exidx.text.uart_send_char), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.uart_send_string), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.uart_send_data), (8 bytes).
    Removing usart_mid.o(.text.fputc), (48 bytes).
    Removing usart_mid.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.my_strlen), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.int_to_string), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.uint_to_hex_string), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.float_to_string), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.uart_printf), (8 bytes).
    Removing usart_mid.o(.text.send_hex_byte), (72 bytes).
    Removing usart_mid.o(.ARM.exidx.text.send_hex_byte), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART_DMA_Init), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_DMA_setSrcAddr), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_DMA_setDestAddr), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_DMA_setTransferSize), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_DMA_enableChannel), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART1_Timeout_Init), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART2_Timeout_Init), (8 bytes).
    Removing usart_mid.o(.text.DMA_REST), (64 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DMA_REST), (8 bytes).
    Removing usart_mid.o(.text.DL_DMA_disableChannel), (38 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_DMA_disableChannel), (8 bytes).
    Removing usart_mid.o(.text.Get_CRC16), (100 bytes).
    Removing usart_mid.o(.ARM.exidx.text.Get_CRC16), (8 bytes).
    Removing usart_mid.o(.text.uint_to_float), (70 bytes).
    Removing usart_mid.o(.ARM.exidx.text.uint_to_float), (8 bytes).
    Removing usart_mid.o(.text.parse_imu_frame), (172 bytes).
    Removing usart_mid.o(.ARM.exidx.text.parse_imu_frame), (8 bytes).
    Removing usart_mid.o(.text.find_frame), (112 bytes).
    Removing usart_mid.o(.ARM.exidx.text.find_frame), (8 bytes).
    Removing usart_mid.o(.text.ProcessReceivedData), (80 bytes).
    Removing usart_mid.o(.ARM.exidx.text.ProcessReceivedData), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART1_CheckTimeout), (8 bytes).
    Removing usart_mid.o(.text.angleToDistance), (88 bytes).
    Removing usart_mid.o(.ARM.exidx.text.angleToDistance), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART1_ProcessData), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.motor_packet_sliding_parse), (8 bytes).
    Removing usart_mid.o(.text.speed_cal_read), (56 bytes).
    Removing usart_mid.o(.ARM.exidx.text.speed_cal_read), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART2_CheckTimeout), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART2_ProcessData), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.vision_packet_sliding_parse), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing usart_mid.o(.ARM.exidx.text.UART3_IRQHandler), (8 bytes).
    Removing usart_mid.o(.rodata.CRC16_table), (512 bytes).
    Removing usart_mid.o(.bss.angle_flag), (1 bytes).
    Removing usart_mid.o(.bss.distance), (8 bytes).
    Removing usart_mid.o(.bss.Motor_Speed_RPM), (4 bytes).
    Removing usart_mid.o(.bss.frameData), (19 bytes).
    Removing usart_mid.o(.bss.currentFrame), (20 bytes).
    Removing gimbal_example.o(.text), (0 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (212 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (112 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (272 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (300 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (148 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (92 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (72 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (74 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (232 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (284 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initFourCCPWMMode), (272 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

555 unused section(s) (total 15581 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/fdiv.c                   0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/frnd.c                   0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/cfplib/scalbn.c                 0x00000000   Number         0  fscalbn.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    Key_App.c                                0x00000000   Number         0  key_app.o ABSOLUTE
    Task_func.c                              0x00000000   Number         0  task_func.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    gimbal_example.c                         0x00000000   Number         0  gimbal_example.o ABSOLUTE
    motor_app.c                              0x00000000   Number         0  motor_app.o ABSOLUTE
    motor_mid.c                              0x00000000   Number         0  motor_mid.o ABSOLUTE
    mydefine.c                               0x00000000   Number         0  mydefine.o ABSOLUTE
    pid_mid.c                                0x00000000   Number         0  pid_mid.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    sine_wave.c                              0x00000000   Number         0  sine_wave.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    usart_mid.c                              0x00000000   Number         0  usart_mid.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000120   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000140   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000148   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x00000164   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000166   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000168   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000016a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000016c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000016c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000016c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000172   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000176   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000176   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000017e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000180   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000180   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000184   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0000018c   Section       44  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001b8   Section      504  aeabi_sdivfast.o(.text)
    .text                                    0x000003b0   Section        0  heapauxi.o(.text)
    .text                                    0x000003b8   Section        0  d2f.o(.text)
    .text                                    0x00000434   Section        0  f2d.o(.text)
    .text                                    0x00000488   Section        0  fdiv.o(.text)
    .text                                    0x000005e8   Section        0  ffixi.o(.text)
    .text                                    0x00000634   Section        0  ffixui.o(.text)
    .text                                    0x00000664   Section        0  fflti.o(.text)
    .text                                    0x000006c2   Section        0  _rserrno.o(.text)
    .text                                    0x000006d8   Section        0  frnd.o(.text)
    .text                                    0x00000758   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x00000760   Section        0  fcmpin.o(.text)
    .text                                    0x000007c4   Section        0  fscalbn.o(.text)
    .text                                    0x00000808   Section        8  libspace.o(.text)
    .text                                    0x00000810   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x0000084e   Section        0  exit.o(.text)
    .text                                    0x0000085e   Section        0  cmpret.o(.text)
    .text                                    0x0000088c   Section        0  fnan2.o(.text)
    .text                                    0x0000089c   Section        0  retnan.o(.text)
    .text                                    0x000008fc   Section        0  sys_exit.o(.text)
    .text                                    0x00000908   Section        2  use_no_semi.o(.text)
    [Anonymous Symbol]                       0x0000090a   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    .text                                    0x0000090a   Section        0  indicate_semi.o(.text)
    DL_Common_updateReg                      0x00000915   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000914   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_DMA_enableChannel                     0x0000093d   Thumb Code    38  usart_mid.o(.text.DL_DMA_enableChannel)
    [Anonymous Symbol]                       0x0000093c   Section        0  usart_mid.o(.text.DL_DMA_enableChannel)
    [Anonymous Symbol]                       0x00000964   Section        0  dl_dma.o(.text.DL_DMA_initChannel)
    __arm_cp.0_0                             0x000009a4   Number         4  dl_dma.o(.text.DL_DMA_initChannel)
    DL_DMA_setDestAddr                       0x000009a9   Thumb Code    36  usart_mid.o(.text.DL_DMA_setDestAddr)
    [Anonymous Symbol]                       0x000009a8   Section        0  usart_mid.o(.text.DL_DMA_setDestAddr)
    __arm_cp.14_0                            0x000009cc   Number         4  usart_mid.o(.text.DL_DMA_setDestAddr)
    DL_DMA_setSrcAddr                        0x000009d1   Thumb Code    36  usart_mid.o(.text.DL_DMA_setSrcAddr)
    [Anonymous Symbol]                       0x000009d0   Section        0  usart_mid.o(.text.DL_DMA_setSrcAddr)
    __arm_cp.13_0                            0x000009f4   Number         4  usart_mid.o(.text.DL_DMA_setSrcAddr)
    DL_DMA_setTransferSize                   0x000009f9   Thumb Code    44  usart_mid.o(.text.DL_DMA_setTransferSize)
    [Anonymous Symbol]                       0x000009f8   Section        0  usart_mid.o(.text.DL_DMA_setTransferSize)
    __arm_cp.15_0                            0x00000a24   Number         4  usart_mid.o(.text.DL_DMA_setTransferSize)
    DL_GPIO_clearPins                        0x00000a29   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000a28   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableOutput                     0x00000a3d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00000a3c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.27_0                            0x00000a50   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00000a55   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00000a54   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_initDigitalInputFeatures         0x00000a69   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    [Anonymous Symbol]                       0x00000a68   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    __arm_cp.25_0                            0x00000a94   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    DL_GPIO_initDigitalOutput                0x00000a99   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x00000a98   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initPeripheralAnalogFunction     0x00000aad   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    [Anonymous Symbol]                       0x00000aac   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    DL_GPIO_initPeripheralInputFunction      0x00000ac1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00000ac0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    __arm_cp.23_0                            0x00000ad8   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralOutputFunction     0x00000add   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x00000adc   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.22_0                            0x00000af4   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x00000af9   Thumb Code    22  key_app.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000af8   Section        0  key_app.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00000b11   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00000b10   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.13_0                            0x00000b20   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.13_1                            0x00000b24   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setPins                          0x00000b29   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000b28   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    __arm_cp.26_0                            0x00000b3c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    DL_MCAN_clearInterruptStatus             0x00000b41   Thumb Code    20  ti_msp_dl_config.o(.text.DL_MCAN_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000b40   Section        0  ti_msp_dl_config.o(.text.DL_MCAN_clearInterruptStatus)
    __arm_cp.51_0                            0x00000b54   Number         4  ti_msp_dl_config.o(.text.DL_MCAN_clearInterruptStatus)
    DL_MCAN_enableInterrupt                  0x00000b59   Thumb Code    24  ti_msp_dl_config.o(.text.DL_MCAN_enableInterrupt)
    [Anonymous Symbol]                       0x00000b58   Section        0  ti_msp_dl_config.o(.text.DL_MCAN_enableInterrupt)
    __arm_cp.52_0                            0x00000b70   Number         4  ti_msp_dl_config.o(.text.DL_MCAN_enableInterrupt)
    [Anonymous Symbol]                       0x00000b74   Section        0  dl_mcan.o(.text.DL_MCAN_enableIntr)
    __arm_cp.25_0                            0x00000b8c   Number         4  dl_mcan.o(.text.DL_MCAN_enableIntr)
    [Anonymous Symbol]                       0x00000b90   Section        0  dl_mcan.o(.text.DL_MCAN_enableIntrLine)
    __arm_cp.28_0                            0x00000ba8   Number         4  dl_mcan.o(.text.DL_MCAN_enableIntrLine)
    DL_MCAN_enableModuleClock                0x00000bad   Thumb Code    18  ti_msp_dl_config.o(.text.DL_MCAN_enableModuleClock)
    [Anonymous Symbol]                       0x00000bac   Section        0  ti_msp_dl_config.o(.text.DL_MCAN_enableModuleClock)
    DL_MCAN_enablePower                      0x00000bc1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_MCAN_enablePower)
    [Anonymous Symbol]                       0x00000bc0   Section        0  ti_msp_dl_config.o(.text.DL_MCAN_enablePower)
    __arm_cp.20_0                            0x00000bd4   Number         4  ti_msp_dl_config.o(.text.DL_MCAN_enablePower)
    [Anonymous Symbol]                       0x00000bd8   Section        0  dl_mcan.o(.text.DL_MCAN_getOpMode)
    [Anonymous Symbol]                       0x00000be4   Section        0  dl_mcan.o(.text.DL_MCAN_getRevisionId)
    [Anonymous Symbol]                       0x00000c28   Section        0  dl_mcan.o(.text.DL_MCAN_init)
    __arm_cp.8_0                             0x00000cf8   Number         4  dl_mcan.o(.text.DL_MCAN_init)
    __arm_cp.8_2                             0x00000cfc   Number         4  dl_mcan.o(.text.DL_MCAN_init)
    [Anonymous Symbol]                       0x00000d00   Section        0  dl_mcan.o(.text.DL_MCAN_isMemInitDone)
    __arm_cp.5_0                             0x00000d0c   Number         4  dl_mcan.o(.text.DL_MCAN_isMemInitDone)
    [Anonymous Symbol]                       0x00000d10   Section        0  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_0                            0x00000f04   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_1                            0x00000f08   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_2                            0x00000f0c   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_3                            0x00000f10   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_4                            0x00000f14   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_5                            0x00000f18   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_6                            0x00000f1c   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_7                            0x00000f20   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    __arm_cp.12_8                            0x00000f24   Number         4  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    DL_MCAN_reset                            0x00000f29   Thumb Code    16  ti_msp_dl_config.o(.text.DL_MCAN_reset)
    [Anonymous Symbol]                       0x00000f28   Section        0  ti_msp_dl_config.o(.text.DL_MCAN_reset)
    __arm_cp.16_0                            0x00000f38   Number         4  ti_msp_dl_config.o(.text.DL_MCAN_reset)
    __arm_cp.16_1                            0x00000f3c   Number         4  ti_msp_dl_config.o(.text.DL_MCAN_reset)
    [Anonymous Symbol]                       0x00000f40   Section        0  dl_mcan.o(.text.DL_MCAN_selectIntrLine)
    __arm_cp.26_0                            0x00000f58   Number         4  dl_mcan.o(.text.DL_MCAN_selectIntrLine)
    [Anonymous Symbol]                       0x00000f5c   Section        0  dl_mcan.o(.text.DL_MCAN_setBitTime)
    __arm_cp.11_0                            0x00001030   Number         4  dl_mcan.o(.text.DL_MCAN_setBitTime)
    __arm_cp.11_1                            0x00001034   Number         4  dl_mcan.o(.text.DL_MCAN_setBitTime)
    __arm_cp.11_2                            0x00001038   Number         4  dl_mcan.o(.text.DL_MCAN_setBitTime)
    [Anonymous Symbol]                       0x0000103c   Section        0  dl_mcan.o(.text.DL_MCAN_setClockConfig)
    __arm_cp.1_1                             0x00001058   Number         4  dl_mcan.o(.text.DL_MCAN_setClockConfig)
    [Anonymous Symbol]                       0x0000105c   Section        0  dl_mcan.o(.text.DL_MCAN_setExtIDAndMask)
    [Anonymous Symbol]                       0x0000108c   Section        0  dl_mcan.o(.text.DL_MCAN_setOpMode)
    __arm_cp.6_0                             0x000010a0   Number         4  dl_mcan.o(.text.DL_MCAN_setOpMode)
    [Anonymous Symbol]                       0x000010a4   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00001158   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x0000115c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00001160   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x00001165   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x00001164   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00001171   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00001170   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.32_0                            0x00001180   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_enableMFCLK                    0x00001185   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    [Anonymous Symbol]                       0x00001184   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    __arm_cp.33_0                            0x00001194   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    DL_SYSCTL_enableMFPCLK                   0x00001199   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK)
    [Anonymous Symbol]                       0x00001198   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK)
    __arm_cp.34_0                            0x000011a4   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK)
    DL_SYSCTL_setBORThreshold                0x000011a9   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x000011a8   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.29_0                            0x000011bc   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x000011c0   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x0000120c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_1                             0x00001210   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_setMFPCLKSource                0x00001215   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource)
    [Anonymous Symbol]                       0x00001214   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource)
    __arm_cp.35_0                            0x00001230   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource)
    DL_SYSCTL_setSYSOSCFreq                  0x00001235   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x00001234   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.30_0                            0x0000124c   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_Timer_enableClock                     0x00001251   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00001250   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enableInterrupt                 0x00001261   Thumb Code    24  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    [Anonymous Symbol]                       0x00001260   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    DL_Timer_enablePower                     0x00001279   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00001278   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x0000128c   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x0000136c   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x00001370   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x00001374   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x00001378   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_reset                           0x0000137d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x0000137c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x0000138c   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_enable                           0x000013a5   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x000013a4   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableDMAReceiveEvent            0x000013bd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    [Anonymous Symbol]                       0x000013bc   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    __arm_cp.41_0                            0x000013d0   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    DL_UART_enableFIFOs                      0x000013d5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    [Anonymous Symbol]                       0x000013d4   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    DL_UART_enableInterrupt                  0x000013ed   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x000013ec   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enableInterrupt                  0x00001405   Thumb Code    24  usart_mid.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x00001404   Section        0  usart_mid.o(.text.DL_UART_enableInterrupt)
    __arm_cp.27_0                            0x0000141c   Number         4  usart_mid.o(.text.DL_UART_enableInterrupt)
    DL_UART_enablePower                      0x00001421   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x00001420   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.19_0                            0x00001434   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x00001439   Thumb Code    18  usart_mid.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001438   Section        0  usart_mid.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x0000144c   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x0000148c   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00001490   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_isBusy                           0x00001495   Thumb Code    20  usart_mid.o(.text.DL_UART_isBusy)
    [Anonymous Symbol]                       0x00001494   Section        0  usart_mid.o(.text.DL_UART_isBusy)
    __arm_cp.1_0                             0x000014a8   Number         4  usart_mid.o(.text.DL_UART_isBusy)
    DL_UART_receiveData                      0x000014ad   Thumb Code    16  usart_mid.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x000014ac   Section        0  usart_mid.o(.text.DL_UART_receiveData)
    __arm_cp.39_0                            0x000014bc   Number         4  usart_mid.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x000014c1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x000014c0   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.15_0                            0x000014d0   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.15_1                            0x000014d4   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x000014d9   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x000014d8   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.39_0                            0x00001514   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.39_1                            0x00001518   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.39_2                            0x0000151c   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.39_3                            0x00001520   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001524   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x00001537   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x00001536   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_setRXFIFOThreshold               0x00001555   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    [Anonymous Symbol]                       0x00001554   Section        0  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    DL_UART_setTXFIFOThreshold               0x00001579   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    [Anonymous Symbol]                       0x00001578   Section        0  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    __arm_cp.44_0                            0x0000159c   Number         4  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    DL_UART_transmitData                     0x000015a1   Thumb Code    22  usart_mid.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x000015a0   Section        0  usart_mid.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x000015b8   Section        0  scheduler.o(.text.GetTick)
    __arm_cp.2_0                             0x00001620   Number         4  scheduler.o(.text.GetTick)
    __arm_cp.2_1                             0x00001624   Number         4  scheduler.o(.text.GetTick)
    __arm_cp.2_2                             0x00001628   Number         4  scheduler.o(.text.GetTick)
    __arm_cp.2_3                             0x0000162c   Number         4  scheduler.o(.text.GetTick)
    __arm_cp.2_4                             0x00001630   Number         4  scheduler.o(.text.GetTick)
    [Anonymous Symbol]                       0x00001634   Section        0  key_app.o(.text.Key_Proc)
    __arm_cp.3_0                             0x000016c4   Number         4  key_app.o(.text.Key_Proc)
    __arm_cp.3_1                             0x000016c8   Number         4  key_app.o(.text.Key_Proc)
    __arm_cp.3_2                             0x000016cc   Number         4  key_app.o(.text.Key_Proc)
    __arm_cp.3_3                             0x000016d0   Number         4  key_app.o(.text.Key_Proc)
    __arm_cp.3_4                             0x000016d4   Number         4  key_app.o(.text.Key_Proc)
    __arm_cp.3_5                             0x000016d8   Number         4  key_app.o(.text.Key_Proc)
    __arm_cp.3_6                             0x000016dc   Number         4  key_app.o(.text.Key_Proc)
    [Anonymous Symbol]                       0x000016e0   Section        0  key_app.o(.text.Key_Read)
    [Anonymous Symbol]                       0x00001754   Section        0  motor_mid.o(.text.Motor_Set_Speed)
    [Anonymous Symbol]                       0x00001868   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.46_0                            0x00001878   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    [Anonymous Symbol]                       0x0000187c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH1_init)
    __arm_cp.48_0                            0x0000188c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH1_init)
    [Anonymous Symbol]                       0x00001890   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH2_init)
    __arm_cp.47_0                            0x000018a0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH2_init)
    __arm_cp.47_1                            0x000018a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH2_init)
    [Anonymous Symbol]                       0x000018a8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    [Anonymous Symbol]                       0x000018b8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x000019b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000019b4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    __arm_cp.10_1                            0x00001a6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    __arm_cp.10_2                            0x00001a70   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    __arm_cp.10_3                            0x00001a74   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    __arm_cp.10_4                            0x00001a78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    __arm_cp.10_5                            0x00001a7c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    __arm_cp.10_6                            0x00001a80   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    __arm_cp.10_7                            0x00001a84   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    [Anonymous Symbol]                       0x00001a88   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00001ac8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001acc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001ad8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.4_0                             0x00001b00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.4_2                             0x00001b04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00001b08   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_0                             0x00001b68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_2                             0x00001b6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00001b70   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_0                             0x00001bb8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_2                             0x00001bbc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x00001bc0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.7_0                             0x00001c20   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.7_2                             0x00001c24   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    [Anonymous Symbol]                       0x00001c28   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00001c64   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00001c68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00001c6c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00001cdc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00001ce0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00001ce4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00001ce8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00001cec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Config                           0x00001cf1   Thumb Code    68  ti_msp_dl_config.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x00001cf0   Section        0  ti_msp_dl_config.o(.text.SysTick_Config)
    __arm_cp.49_0                            0x00001d34   Number         4  ti_msp_dl_config.o(.text.SysTick_Config)
    __arm_cp.49_1                            0x00001d38   Number         4  ti_msp_dl_config.o(.text.SysTick_Config)
    __arm_cp.49_2                            0x00001d3c   Number         4  ti_msp_dl_config.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x00001d40   Section        0  scheduler.o(.text.SysTick_Handler)
    __arm_cp.1_1                             0x00001d50   Number         4  scheduler.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x00001d54   Section        0  scheduler.o(.text.SystemTime_Init)
    __arm_cp.0_0                             0x00001d5c   Number         4  scheduler.o(.text.SystemTime_Init)
    [Anonymous Symbol]                       0x00001d60   Section        0  usart_mid.o(.text.UART0_IRQHandler)
    __arm_cp.36_0                            0x00001d7c   Number         4  usart_mid.o(.text.UART0_IRQHandler)
    __arm_cp.36_1                            0x00001d80   Number         4  usart_mid.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00001d84   Section        0  usart_mid.o(.text.UART1_CheckTimeout)
    __arm_cp.28_1                            0x00001df0   Number         4  usart_mid.o(.text.UART1_CheckTimeout)
    [Anonymous Symbol]                       0x00001df4   Section        0  usart_mid.o(.text.UART1_IRQHandler)
    [Anonymous Symbol]                       0x00001e64   Section        0  usart_mid.o(.text.UART1_ProcessData)
    __arm_cp.30_1                            0x00001ec8   Number         4  usart_mid.o(.text.UART1_ProcessData)
    [Anonymous Symbol]                       0x00001ecc   Section        0  usart_mid.o(.text.UART1_Timeout_Init)
    __arm_cp.18_0                            0x00001f20   Number         4  usart_mid.o(.text.UART1_Timeout_Init)
    __arm_cp.18_1                            0x00001f24   Number         4  usart_mid.o(.text.UART1_Timeout_Init)
    [Anonymous Symbol]                       0x00001f28   Section        0  usart_mid.o(.text.UART2_CheckTimeout)
    [Anonymous Symbol]                       0x00001f7c   Section        0  usart_mid.o(.text.UART2_ProcessData)
    [Anonymous Symbol]                       0x00001fd8   Section        0  usart_mid.o(.text.UART2_Timeout_Init)
    __arm_cp.19_1                            0x00002028   Number         4  usart_mid.o(.text.UART2_Timeout_Init)
    [Anonymous Symbol]                       0x0000202c   Section        0  usart_mid.o(.text.UART3_IRQHandler)
    __arm_cp.40_0                            0x0000209c   Number         4  usart_mid.o(.text.UART3_IRQHandler)
    __arm_cp.40_1                            0x000020a0   Number         4  usart_mid.o(.text.UART3_IRQHandler)
    __arm_cp.40_2                            0x000020a4   Number         4  usart_mid.o(.text.UART3_IRQHandler)
    [Anonymous Symbol]                       0x000020a8   Section        0  usart_mid.o(.text.UART_DMA_Init)
    __arm_cp.12_0                            0x000020f0   Number         4  usart_mid.o(.text.UART_DMA_Init)
    __arm_cp.12_1                            0x000020f4   Number         4  usart_mid.o(.text.UART_DMA_Init)
    __arm_cp.12_2                            0x000020f8   Number         4  usart_mid.o(.text.UART_DMA_Init)
    __NVIC_EnableIRQ                         0x000020fd   Thumb Code    40  usart_mid.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x000020fc   Section        0  usart_mid.o(.text.__NVIC_EnableIRQ)
    __arm_cp.17_0                            0x00002124   Number         4  usart_mid.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x00002129   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00002128   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.54_0                            0x000021a4   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.54_1                            0x000021a8   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x000021ac   Section        0  motor_app.o(.text.app_pid_calc)
    __arm_cp.5_8                             0x00002388   Number         4  motor_app.o(.text.app_pid_calc)
    __arm_cp.5_9                             0x0000238c   Number         4  motor_app.o(.text.app_pid_calc)
    __arm_cp.5_10                            0x00002390   Number         4  motor_app.o(.text.app_pid_calc)
    __arm_cp.5_12                            0x00002394   Number         4  motor_app.o(.text.app_pid_calc)
    __arm_cp.5_13                            0x00002398   Number         4  motor_app.o(.text.app_pid_calc)
    __arm_cp.5_14                            0x0000239c   Number         4  motor_app.o(.text.app_pid_calc)
    __arm_cp.5_15                            0x000023a0   Number         4  motor_app.o(.text.app_pid_calc)
    __arm_cp.5_16                            0x000023a4   Number         4  motor_app.o(.text.app_pid_calc)
    [Anonymous Symbol]                       0x000023a8   Section        0  motor_app.o(.text.app_pid_init)
    [Anonymous Symbol]                       0x000023f0   Section        0  pid_mid.o(.text.app_pid_limit_integral)
    [Anonymous Symbol]                       0x00002430   Section        0  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_0                             0x0000256c   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_1                             0x00002570   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_2                             0x00002574   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_3                             0x00002578   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_4                             0x0000257c   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_5                             0x00002580   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_6                             0x00002584   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_7                             0x00002588   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_8                             0x0000258c   Number         4  sine_wave.o(.text.common_pid_control)
    __arm_cp.1_9                             0x00002590   Number         4  sine_wave.o(.text.common_pid_control)
    constrain_angle                          0x00002595   Thumb Code    72  motor_mid.o(.text.constrain_angle)
    [Anonymous Symbol]                       0x00002594   Section        0  motor_mid.o(.text.constrain_angle)
    [Anonymous Symbol]                       0x000025dc   Section        0  task_func.o(.text.contorol_Task)
    __arm_cp.0_0                             0x00002610   Number         4  task_func.o(.text.contorol_Task)
    __arm_cp.0_1                             0x00002614   Number         4  task_func.o(.text.contorol_Task)
    [Anonymous Symbol]                       0x00002618   Section        0  scheduler.o(.text.delay_ms)
    [Anonymous Symbol]                       0x0000263c   Section        0  motor_mid.o(.text.dis_read)
    __arm_cp.10_0                            0x00002668   Number         4  motor_mid.o(.text.dis_read)
    [Anonymous Symbol]                       0x0000266c   Section        0  key_app.o(.text.example_pixel_coordinate_control)
    __arm_cp.2_0                             0x000026cc   Number         4  key_app.o(.text.example_pixel_coordinate_control)
    __arm_cp.2_2                             0x000026d0   Number         4  key_app.o(.text.example_pixel_coordinate_control)
    __arm_cp.2_3                             0x000026d4   Number         4  key_app.o(.text.example_pixel_coordinate_control)
    __arm_cp.2_4                             0x000026d8   Number         4  key_app.o(.text.example_pixel_coordinate_control)
    [Anonymous Symbol]                       0x000026dc   Section        0  usart_mid.o(.text.float_to_string)
    __arm_cp.9_0                             0x000027b8   Number         4  usart_mid.o(.text.float_to_string)
    [Anonymous Symbol]                       0x000027bc   Section        0  motor_app.o(.text.get_data)
    __arm_cp.7_0                             0x00002840   Number         4  motor_app.o(.text.get_data)
    __arm_cp.7_1                             0x00002844   Number         4  motor_app.o(.text.get_data)
    __arm_cp.7_2                             0x00002848   Number         4  motor_app.o(.text.get_data)
    __arm_cp.7_3                             0x0000284c   Number         4  motor_app.o(.text.get_data)
    [Anonymous Symbol]                       0x00002850   Section        0  usart_mid.o(.text.int_to_string)
    [Anonymous Symbol]                       0x00002918   Section        0  sine_wave.o(.text.is_target_reached)
    __arm_cp.0_0                             0x00002978   Number         4  sine_wave.o(.text.is_target_reached)
    __arm_cp.0_1                             0x0000297c   Number         4  sine_wave.o(.text.is_target_reached)
    __arm_cp.0_2                             0x00002980   Number         4  sine_wave.o(.text.is_target_reached)
    __arm_cp.0_3                             0x00002984   Number         4  sine_wave.o(.text.is_target_reached)
    [Anonymous Symbol]                       0x00002988   Section        0  empty.o(.text.main)
    [Anonymous Symbol]                       0x000029b0   Section        0  motor_mid.o(.text.motor_distance)
    __arm_cp.2_0                             0x00002a20   Number         4  motor_mid.o(.text.motor_distance)
    [Anonymous Symbol]                       0x00002a24   Section        0  motor_mid.o(.text.motor_init)
    __arm_cp.0_0                             0x00002a60   Number         4  motor_mid.o(.text.motor_init)
    [Anonymous Symbol]                       0x00002a64   Section        0  motor_mid.o(.text.motor_move_angle)
    __arm_cp.17_0                            0x00002b74   Number         4  motor_mid.o(.text.motor_move_angle)
    [Anonymous Symbol]                       0x00002b78   Section        0  usart_mid.o(.text.motor_packet_sliding_parse)
    __arm_cp.31_0                            0x00002cfc   Number         4  usart_mid.o(.text.motor_packet_sliding_parse)
    __arm_cp.31_1                            0x00002d00   Number         4  usart_mid.o(.text.motor_packet_sliding_parse)
    __arm_cp.31_2                            0x00002d04   Number         4  usart_mid.o(.text.motor_packet_sliding_parse)
    __arm_cp.31_3                            0x00002d08   Number         4  usart_mid.o(.text.motor_packet_sliding_parse)
    [Anonymous Symbol]                       0x00002d0c   Section        0  motor_mid.o(.text.motor_readdistance)
    __arm_cp.4_0                             0x00002d2c   Number         4  motor_mid.o(.text.motor_readdistance)
    [Anonymous Symbol]                       0x00002d30   Section        0  motor_mid.o(.text.motor_speed)
    __arm_cp.3_0                             0x00002d7c   Number         4  motor_mid.o(.text.motor_speed)
    [Anonymous Symbol]                       0x00002d80   Section        0  motor_mid.o(.text.motor_stop)
    __arm_cp.1_0                             0x00002d94   Number         4  motor_mid.o(.text.motor_stop)
    [Anonymous Symbol]                       0x00002d98   Section        0  motor_mid.o(.text.motor_sync)
    __arm_cp.8_0                             0x00002dac   Number         4  motor_mid.o(.text.motor_sync)
    __arm_cp.8_1                             0x00002db0   Number         4  motor_mid.o(.text.motor_sync)
    [Anonymous Symbol]                       0x00002db4   Section        0  motor_mid.o(.text.move_to_pixel)
    [Anonymous Symbol]                       0x00002de6   Section        0  usart_mid.o(.text.my_strlen)
    [Anonymous Symbol]                       0x00002e14   Section        0  pid_mid.o(.text.pid_calculate_positional)
    pid_formula_positional                   0x00002e37   Thumb Code   132  pid_mid.o(.text.pid_formula_positional)
    [Anonymous Symbol]                       0x00002e36   Section        0  pid_mid.o(.text.pid_formula_positional)
    [Anonymous Symbol]                       0x00002eba   Section        0  pid_mid.o(.text.pid_init)
    pid_out_limit                            0x00002f0b   Thumb Code    72  pid_mid.o(.text.pid_out_limit)
    [Anonymous Symbol]                       0x00002f0a   Section        0  pid_mid.o(.text.pid_out_limit)
    [Anonymous Symbol]                       0x00002f52   Section        0  pid_mid.o(.text.pid_set_target)
    [Anonymous Symbol]                       0x00002f64   Section        0  motor_app.o(.text.pid_target)
    __arm_cp.4_0                             0x00002f9c   Number         4  motor_app.o(.text.pid_target)
    __arm_cp.4_1                             0x00002fa0   Number         4  motor_app.o(.text.pid_target)
    __arm_cp.4_2                             0x00002fa4   Number         4  motor_app.o(.text.pid_target)
    __arm_cp.4_3                             0x00002fa8   Number         4  motor_app.o(.text.pid_target)
    [Anonymous Symbol]                       0x00002fac   Section        0  motor_mid.o(.text.pixel_to_angle_x)
    __arm_cp.14_0                            0x00002ffc   Number         4  motor_mid.o(.text.pixel_to_angle_x)
    __arm_cp.14_1                            0x00003000   Number         4  motor_mid.o(.text.pixel_to_angle_x)
    __arm_cp.14_2                            0x00003004   Number         4  motor_mid.o(.text.pixel_to_angle_x)
    __arm_cp.14_3                            0x00003008   Number         4  motor_mid.o(.text.pixel_to_angle_x)
    __arm_cp.14_4                            0x0000300c   Number         4  motor_mid.o(.text.pixel_to_angle_x)
    [Anonymous Symbol]                       0x00003010   Section        0  motor_mid.o(.text.pixel_to_angle_y)
    __arm_cp.15_0                            0x00003060   Number         4  motor_mid.o(.text.pixel_to_angle_y)
    __arm_cp.15_1                            0x00003064   Number         4  motor_mid.o(.text.pixel_to_angle_y)
    __arm_cp.15_2                            0x00003068   Number         4  motor_mid.o(.text.pixel_to_angle_y)
    [Anonymous Symbol]                       0x0000306c   Section        0  motor_app.o(.text.read_place)
    __arm_cp.3_0                             0x00003084   Number         4  motor_app.o(.text.read_place)
    __arm_cp.3_1                             0x00003088   Number         4  motor_app.o(.text.read_place)
    [Anonymous Symbol]                       0x0000308c   Section        0  scheduler.o(.text.scheduler_init)
    [Anonymous Symbol]                       0x00003094   Section        0  scheduler.o(.text.scheduler_run)
    __arm_cp.4_0                             0x000030fc   Number         4  scheduler.o(.text.scheduler_run)
    __arm_cp.4_1                             0x00003100   Number         4  scheduler.o(.text.scheduler_run)
    [Anonymous Symbol]                       0x00003104   Section        0  sine_wave.o(.text.sine_wave_calculate_point)
    __arm_cp.7_2                             0x000031dc   Number         4  sine_wave.o(.text.sine_wave_calculate_point)
    __arm_cp.7_3                             0x000031e0   Number         4  sine_wave.o(.text.sine_wave_calculate_point)
    [Anonymous Symbol]                       0x000031e4   Section        0  sine_wave.o(.text.sine_wave_pid_control)
    __arm_cp.10_0                            0x000031f0   Number         4  sine_wave.o(.text.sine_wave_pid_control)
    [Anonymous Symbol]                       0x000031f4   Section        0  sine_wave.o(.text.sine_wave_task)
    __arm_cp.9_0                             0x0000327c   Number         4  sine_wave.o(.text.sine_wave_task)
    __arm_cp.9_1                             0x00003280   Number         4  sine_wave.o(.text.sine_wave_task)
    __arm_cp.9_2                             0x00003284   Number         4  sine_wave.o(.text.sine_wave_task)
    __arm_cp.9_3                             0x00003288   Number         4  sine_wave.o(.text.sine_wave_task)
    __arm_cp.9_4                             0x0000328c   Number         4  sine_wave.o(.text.sine_wave_task)
    __arm_cp.9_6                             0x00003290   Number         4  sine_wave.o(.text.sine_wave_task)
    [Anonymous Symbol]                       0x00003294   Section        0  sine_wave.o(.text.triangle_pid_control)
    __arm_cp.17_0                            0x000032a0   Number         4  sine_wave.o(.text.triangle_pid_control)
    [Anonymous Symbol]                       0x000032a4   Section        0  sine_wave.o(.text.triangle_task)
    __arm_cp.16_0                            0x00003338   Number         4  sine_wave.o(.text.triangle_task)
    __arm_cp.16_1                            0x0000333c   Number         4  sine_wave.o(.text.triangle_task)
    __arm_cp.16_2                            0x00003340   Number         4  sine_wave.o(.text.triangle_task)
    __arm_cp.16_3                            0x00003344   Number         4  sine_wave.o(.text.triangle_task)
    __arm_cp.16_4                            0x00003348   Number         4  sine_wave.o(.text.triangle_task)
    __arm_cp.16_5                            0x0000334c   Number         4  sine_wave.o(.text.triangle_task)
    [Anonymous Symbol]                       0x00003350   Section        0  usart_mid.o(.text.uart_printf)
    [Anonymous Symbol]                       0x00003584   Section        0  usart_mid.o(.text.uart_send_char)
    [Anonymous Symbol]                       0x000035ac   Section        0  usart_mid.o(.text.uart_send_data)
    [Anonymous Symbol]                       0x000035e0   Section        0  usart_mid.o(.text.uart_send_string)
    [Anonymous Symbol]                       0x00003614   Section        0  usart_mid.o(.text.uint_to_hex_string)
    __arm_cp.8_0                             0x000036bc   Number         4  usart_mid.o(.text.uint_to_hex_string)
    __arm_cp.8_1                             0x000036c0   Number         4  usart_mid.o(.text.uint_to_hex_string)
    [Anonymous Symbol]                       0x000036c4   Section        0  sine_wave.o(.text.unified_motion_task)
    __arm_cp.18_0                            0x00003714   Number         4  sine_wave.o(.text.unified_motion_task)
    __arm_cp.18_1                            0x00003718   Number         4  sine_wave.o(.text.unified_motion_task)
    __arm_cp.18_2                            0x0000371c   Number         4  sine_wave.o(.text.unified_motion_task)
    __arm_cp.18_3                            0x00003720   Number         4  sine_wave.o(.text.unified_motion_task)
    __arm_cp.18_4                            0x00003724   Number         4  sine_wave.o(.text.unified_motion_task)
    __arm_cp.18_5                            0x00003728   Number         4  sine_wave.o(.text.unified_motion_task)
    [Anonymous Symbol]                       0x0000372c   Section        0  usart_mid.o(.text.vision_packet_sliding_parse)
    __arm_cp.35_0                            0x000039e8   Number         4  usart_mid.o(.text.vision_packet_sliding_parse)
    __arm_cp.35_1                            0x000039ec   Number         4  usart_mid.o(.text.vision_packet_sliding_parse)
    __arm_cp.35_2                            0x000039f0   Number         4  usart_mid.o(.text.vision_packet_sliding_parse)
    .text_divfast                            0x000039f4   Section      502  aeabi_sdivfast.o(.text_divfast)
    i.__ARM_common_ll_muluu                  0x00003bea   Section        0  rredf.o(i.__ARM_common_ll_muluu)
    i.__ARM_fpclassifyf                      0x00003c1c   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__mathlib_flt_infnan                   0x00003c3e   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x00003c48   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x00003c54   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x00003c64   Section        0  rredf.o(i.__mathlib_rredf2)
    i._fgeq                                  0x00003de4   Section        0  fcmp.o(i._fgeq)
    i._fgr                                   0x00003dfa   Section        0  fcmp.o(i._fgr)
    i._fleq                                  0x00003e10   Section        0  fcmp.o(i._fleq)
    i._fls                                   0x00003e2a   Section        0  fcmp.o(i._fls)
    i.sinf                                   0x00003e40   Section        0  sinf.o(i.sinf)
    x$fpl$fadd                               0x00003fc4   Section      140  faddsub.o(x$fpl$fadd)
    _fadd1                                   0x00003fd1   Thumb Code     0  faddsub.o(x$fpl$fadd)
    x$fpl$fgeqf                              0x00004050   Section       84  fgef.o(x$fpl$fgeqf)
    x$fpl$fleqf                              0x000040a4   Section       84  flef.o(x$fpl$fleqf)
    x$fpl$fmul                               0x000040f8   Section      176  fmul.o(x$fpl$fmul)
    x$fpl$frsb                               0x000041a8   Section       24  faddsub.o(x$fpl$frsb)
    x$fpl$fsub                               0x000041c0   Section      208  faddsub.o(x$fpl$fsub)
    _fsub1                                   0x000041cd   Thumb Code     0  faddsub.o(x$fpl$fsub)
    fdiv_tab                                 0x00004290   Data          64  fdiv.o(.constdata)
    .constdata                               0x00004290   Section       64  fdiv.o(.constdata)
    x$fpl$usenofp                            0x00004290   Section        0  usenofp.o(x$fpl$usenofp)
    twooverpi                                0x000042d0   Data          32  rredf.o(.constdata)
    .constdata                               0x000042d0   Section       32  rredf.o(.constdata)
    gDMA_CH0Config                           0x000042f0   Data          24  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    [Anonymous Symbol]                       0x000042f0   Section        0  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    gDMA_CH1Config                           0x00004308   Data          24  ti_msp_dl_config.o(.rodata.gDMA_CH1Config)
    [Anonymous Symbol]                       0x00004308   Section        0  ti_msp_dl_config.o(.rodata.gDMA_CH1Config)
    gDMA_CH2Config                           0x00004320   Data          24  ti_msp_dl_config.o(.rodata.gDMA_CH2Config)
    [Anonymous Symbol]                       0x00004320   Section        0  ti_msp_dl_config.o(.rodata.gDMA_CH2Config)
    gMCAN0BitTimes                           0x00004338   Data          32  ti_msp_dl_config.o(.rodata.gMCAN0BitTimes)
    [Anonymous Symbol]                       0x00004338   Section        0  ti_msp_dl_config.o(.rodata.gMCAN0BitTimes)
    gMCAN0ClockConf                          0x00004358   Data           4  ti_msp_dl_config.o(.rodata.gMCAN0ClockConf)
    [Anonymous Symbol]                       0x00004358   Section        0  ti_msp_dl_config.o(.rodata.gMCAN0ClockConf)
    gMCAN0InitParams                         0x0000435c   Data          52  ti_msp_dl_config.o(.rodata.gMCAN0InitParams)
    [Anonymous Symbol]                       0x0000435c   Section        0  ti_msp_dl_config.o(.rodata.gMCAN0InitParams)
    gMCAN0MsgRAMConfigParams                 0x00004390   Data          96  ti_msp_dl_config.o(.rodata.gMCAN0MsgRAMConfigParams)
    [Anonymous Symbol]                       0x00004390   Section        0  ti_msp_dl_config.o(.rodata.gMCAN0MsgRAMConfigParams)
    gSYSPLLConfig                            0x000043f0   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x000043f0   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_0ClockConfig                      0x00004418   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00004418   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x0000441c   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x0000441c   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x00004430   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00004430   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00004432   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00004432   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_1ClockConfig                       0x0000443c   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x0000443c   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x0000443e   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x0000443e   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    gUART_2ClockConfig                       0x00004448   Data           2  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    [Anonymous Symbol]                       0x00004448   Section        0  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    gUART_2Config                            0x0000444a   Data          10  ti_msp_dl_config.o(.rodata.gUART_2Config)
    [Anonymous Symbol]                       0x0000444a   Section        0  ti_msp_dl_config.o(.rodata.gUART_2Config)
    [Anonymous Symbol]                       0x00004454   Section        0  key_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x000044dc   Section        0  motor_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x00004507   Section        0  sine_wave.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x000048f7   Section        0  usart_mid.o(.rodata.str1.1)
    scheduler_task                           0x20200070   Data          84  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20200070   Section        0  scheduler.o(.data.scheduler_task)
    sine_params                              0x202000c8   Data          24  sine_wave.o(.data.sine_params)
    [Anonymous Symbol]                       0x202000c8   Section        0  sine_wave.o(.data.sine_params)
    .bss                                     0x202000f0   Section       96  libspace.o(.bss)
    GetTick.last_time                        0x20200150   Data           4  scheduler.o(.bss.GetTick.last_time)
    [Anonymous Symbol]                       0x20200150   Section        0  scheduler.o(.bss.GetTick.last_time)
    dis_read.read_count                      0x2020016c   Data           1  motor_mid.o(.bss.dis_read.read_count)
    [Anonymous Symbol]                       0x2020016c   Section        0  motor_mid.o(.bss.dis_read.read_count)
    motor_packet_sliding_parse.rxBuffer      0x202002ef   Data         128  usart_mid.o(.bss.motor_packet_sliding_parse.rxBuffer)
    [Anonymous Symbol]                       0x202002ef   Section        0  usart_mid.o(.bss.motor_packet_sliding_parse.rxBuffer)
    motor_packet_sliding_parse.rxCounter     0x20200370   Data           2  usart_mid.o(.bss.motor_packet_sliding_parse.rxCounter)
    [Anonymous Symbol]                       0x20200370   Section        0  usart_mid.o(.bss.motor_packet_sliding_parse.rxCounter)
    systemTimeMs                             0x202003f8   Data           4  scheduler.o(.bss.systemTimeMs)
    [Anonymous Symbol]                       0x202003f8   Section        0  scheduler.o(.bss.systemTimeMs)
    systemTimeMs_backup                      0x202003fc   Data           4  scheduler.o(.bss.systemTimeMs_backup)
    [Anonymous Symbol]                       0x202003fc   Section        0  scheduler.o(.bss.systemTimeMs_backup)
    time_error_count                         0x20200404   Data           4  scheduler.o(.bss.time_error_count)
    [Anonymous Symbol]                       0x20200404   Section        0  scheduler.o(.bss.time_error_count)
    uart1_data_count                         0x20200418   Data           4  usart_mid.o(.bss.uart1_data_count)
    [Anonymous Symbol]                       0x20200418   Section        0  usart_mid.o(.bss.uart1_data_count)
    unified_motion_task.debug_count          0x20200420   Data           4  sine_wave.o(.bss.unified_motion_task.debug_count)
    [Anonymous Symbol]                       0x20200420   Section        0  sine_wave.o(.bss.unified_motion_task.debug_count)
    vision_packet_sliding_parse.rxBuffer     0x20200424   Data         128  usart_mid.o(.bss.vision_packet_sliding_parse.rxBuffer)
    [Anonymous Symbol]                       0x20200424   Section        0  usart_mid.o(.bss.vision_packet_sliding_parse.rxBuffer)
    vision_packet_sliding_parse.rxCounter    0x202004a4   Data           2  usart_mid.o(.bss.vision_packet_sliding_parse.rxCounter)
    [Anonymous Symbol]                       0x202004a4   Section        0  usart_mid.o(.bss.vision_packet_sliding_parse.rxCounter)
    Heap_Mem                                 0x202004d0   Data           0  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x202004d0   Data         256  startup_mspm0g350x_uvision.o(STACK)
    HEAP                                     0x202004d0   Section        0  startup_mspm0g350x_uvision.o(HEAP)
    STACK                                    0x202004d0   Section      256  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x202005d0   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000121   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x00000141   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000149   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x00000165   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000169   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000016d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000016d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000016d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000177   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000177   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000017f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000185   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0000018d   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x00000191   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x00000193   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x00000195   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x00000197   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x0000019b   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x0000019d   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __aeabi_uidivmod                         0x000001b9   Thumb Code    28  aeabi_sdivfast.o(.text)
    __aeabi_idivmod                          0x000001d5   Thumb Code   472  aeabi_sdivfast.o(.text)
    __use_two_region_memory                  0x000003b1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x000003b3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x000003b5   Thumb Code     2  heapauxi.o(.text)
    __aeabi_d2f                              0x000003b9   Thumb Code     0  d2f.o(.text)
    __truncdfsf2                             0x000003b9   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x000003b9   Thumb Code   120  d2f.o(.text)
    __aeabi_f2d                              0x00000435   Thumb Code     0  f2d.o(.text)
    __extendsfdf2                            0x00000435   Thumb Code     0  f2d.o(.text)
    _f2d                                     0x00000435   Thumb Code    80  f2d.o(.text)
    __aeabi_fdiv                             0x00000489   Thumb Code     0  fdiv.o(.text)
    __divsf3                                 0x00000489   Thumb Code     0  fdiv.o(.text)
    _fdiv                                    0x00000489   Thumb Code   334  fdiv.o(.text)
    _frdiv                                   0x000005d7   Thumb Code     8  fdiv.o(.text)
    __aeabi_f2iz                             0x000005e9   Thumb Code     0  ffixi.o(.text)
    _ffix                                    0x000005e9   Thumb Code    76  ffixi.o(.text)
    __aeabi_f2uiz                            0x00000635   Thumb Code     0  ffixui.o(.text)
    _ffixu                                   0x00000635   Thumb Code    48  ffixui.o(.text)
    __aeabi_i2f_normalise                    0x00000665   Thumb Code    72  fflti.o(.text)
    __aeabi_i2f                              0x000006ad   Thumb Code    16  fflti.o(.text)
    _fflt                                    0x000006ad   Thumb Code     0  fflti.o(.text)
    __aeabi_ui2f                             0x000006bd   Thumb Code     6  fflti.o(.text)
    _ffltu                                   0x000006bd   Thumb Code     0  fflti.o(.text)
    __read_errno                             0x000006c3   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x000006cd   Thumb Code    12  _rserrno.o(.text)
    _frnd                                    0x000006d9   Thumb Code   122  frnd.o(.text)
    __aeabi_errno_addr                       0x00000759   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x00000759   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x00000759   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __fpl_fcmp_InfNaN                        0x00000761   Thumb Code    96  fcmpin.o(.text)
    __ARM_scalbnf                            0x000007c5   Thumb Code    64  fscalbn.o(.text)
    __user_libspace                          0x00000809   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x00000809   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x00000809   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x00000811   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x0000084f   Thumb Code    16  exit.o(.text)
    __fpl_cmpreturn                          0x0000085f   Thumb Code    46  cmpret.o(.text)
    __fpl_fcheck_NaN2                        0x0000088d   Thumb Code    10  fnan2.o(.text)
    __fpl_return_NaN                         0x0000089d   Thumb Code    94  retnan.o(.text)
    _sys_exit                                0x000008fd   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x00000909   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x00000909   Thumb Code     2  use_no_semi.o(.text)
    DL_Common_delayCycles                    0x0000090b   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    __semihosting_library_function           0x0000090b   Thumb Code     0  indicate_semi.o(.text)
    DL_DMA_initChannel                       0x00000965   Thumb Code    68  dl_dma.o(.text.DL_DMA_initChannel)
    DL_MCAN_enableIntr                       0x00000b75   Thumb Code    28  dl_mcan.o(.text.DL_MCAN_enableIntr)
    DL_MCAN_enableIntrLine                   0x00000b91   Thumb Code    28  dl_mcan.o(.text.DL_MCAN_enableIntrLine)
    DL_MCAN_getOpMode                        0x00000bd9   Thumb Code    12  dl_mcan.o(.text.DL_MCAN_getOpMode)
    DL_MCAN_getRevisionId                    0x00000be5   Thumb Code    68  dl_mcan.o(.text.DL_MCAN_getRevisionId)
    DL_MCAN_init                             0x00000c29   Thumb Code   216  dl_mcan.o(.text.DL_MCAN_init)
    DL_MCAN_isMemInitDone                    0x00000d01   Thumb Code    16  dl_mcan.o(.text.DL_MCAN_isMemInitDone)
    DL_MCAN_msgRAMConfig                     0x00000d11   Thumb Code   536  dl_mcan.o(.text.DL_MCAN_msgRAMConfig)
    DL_MCAN_selectIntrLine                   0x00000f41   Thumb Code    28  dl_mcan.o(.text.DL_MCAN_selectIntrLine)
    DL_MCAN_setBitTime                       0x00000f5d   Thumb Code   224  dl_mcan.o(.text.DL_MCAN_setBitTime)
    DL_MCAN_setClockConfig                   0x0000103d   Thumb Code    32  dl_mcan.o(.text.DL_MCAN_setClockConfig)
    DL_MCAN_setExtIDAndMask                  0x0000105d   Thumb Code    48  dl_mcan.o(.text.DL_MCAN_setExtIDAndMask)
    DL_MCAN_setOpMode                        0x0000108d   Thumb Code    24  dl_mcan.o(.text.DL_MCAN_setOpMode)
    DL_SYSCTL_configSYSPLL                   0x000010a5   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x000011c1   Thumb Code    84  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_Timer_initTimerMode                   0x0000128d   Thumb Code   240  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setClockConfig                  0x0000138d   Thumb Code    24  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x0000144d   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00001525   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    GetTick                                  0x000015b9   Thumb Code   104  scheduler.o(.text.GetTick)
    Key_Proc                                 0x00001635   Thumb Code   144  key_app.o(.text.Key_Proc)
    Key_Read                                 0x000016e1   Thumb Code   116  key_app.o(.text.Key_Read)
    Motor_Set_Speed                          0x00001755   Thumb Code   274  motor_mid.o(.text.Motor_Set_Speed)
    SYSCFG_DL_DMA_CH0_init                   0x00001869   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    SYSCFG_DL_DMA_CH1_init                   0x0000187d   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH1_init)
    SYSCFG_DL_DMA_CH2_init                   0x00001891   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH2_init)
    SYSCFG_DL_DMA_init                       0x000018a9   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    SYSCFG_DL_GPIO_init                      0x000018b9   Thumb Code   248  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_MCAN0_init                     0x000019b5   Thumb Code   184  ti_msp_dl_config.o(.text.SYSCFG_DL_MCAN0_init)
    SYSCFG_DL_SYSCTL_init                    0x00001a89   Thumb Code    64  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00001acd   Thumb Code    12  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00001ad9   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x00001b09   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_1_init                    0x00001b71   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_UART_2_init                    0x00001bc1   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    SYSCFG_DL_init                           0x00001c29   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00001c6d   Thumb Code   112  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Handler                          0x00001d41   Thumb Code    16  scheduler.o(.text.SysTick_Handler)
    SystemTime_Init                          0x00001d55   Thumb Code     8  scheduler.o(.text.SystemTime_Init)
    UART0_IRQHandler                         0x00001d61   Thumb Code    28  usart_mid.o(.text.UART0_IRQHandler)
    UART1_CheckTimeout                       0x00001d85   Thumb Code   108  usart_mid.o(.text.UART1_CheckTimeout)
    UART1_IRQHandler                         0x00001df5   Thumb Code   112  usart_mid.o(.text.UART1_IRQHandler)
    UART1_ProcessData                        0x00001e65   Thumb Code   100  usart_mid.o(.text.UART1_ProcessData)
    UART1_Timeout_Init                       0x00001ecd   Thumb Code    84  usart_mid.o(.text.UART1_Timeout_Init)
    UART2_CheckTimeout                       0x00001f29   Thumb Code    84  usart_mid.o(.text.UART2_CheckTimeout)
    UART2_ProcessData                        0x00001f7d   Thumb Code    92  usart_mid.o(.text.UART2_ProcessData)
    UART2_Timeout_Init                       0x00001fd9   Thumb Code    80  usart_mid.o(.text.UART2_Timeout_Init)
    UART3_IRQHandler                         0x0000202d   Thumb Code   112  usart_mid.o(.text.UART3_IRQHandler)
    UART_DMA_Init                            0x000020a9   Thumb Code    72  usart_mid.o(.text.UART_DMA_Init)
    app_pid_calc                             0x000021ad   Thumb Code   476  motor_app.o(.text.app_pid_calc)
    app_pid_init                             0x000023a9   Thumb Code    72  motor_app.o(.text.app_pid_init)
    app_pid_limit_integral                   0x000023f1   Thumb Code    64  pid_mid.o(.text.app_pid_limit_integral)
    common_pid_control                       0x00002431   Thumb Code   316  sine_wave.o(.text.common_pid_control)
    contorol_Task                            0x000025dd   Thumb Code    52  task_func.o(.text.contorol_Task)
    delay_ms                                 0x00002619   Thumb Code    36  scheduler.o(.text.delay_ms)
    dis_read                                 0x0000263d   Thumb Code    44  motor_mid.o(.text.dis_read)
    example_pixel_coordinate_control         0x0000266d   Thumb Code    96  key_app.o(.text.example_pixel_coordinate_control)
    float_to_string                          0x000026dd   Thumb Code   220  usart_mid.o(.text.float_to_string)
    get_data                                 0x000027bd   Thumb Code   132  motor_app.o(.text.get_data)
    int_to_string                            0x00002851   Thumb Code   198  usart_mid.o(.text.int_to_string)
    is_target_reached                        0x00002919   Thumb Code    96  sine_wave.o(.text.is_target_reached)
    main                                     0x00002989   Thumb Code    40  empty.o(.text.main)
    motor_distance                           0x000029b1   Thumb Code   112  motor_mid.o(.text.motor_distance)
    motor_init                               0x00002a25   Thumb Code    60  motor_mid.o(.text.motor_init)
    motor_move_angle                         0x00002a65   Thumb Code   272  motor_mid.o(.text.motor_move_angle)
    motor_packet_sliding_parse               0x00002b79   Thumb Code   388  usart_mid.o(.text.motor_packet_sliding_parse)
    motor_readdistance                       0x00002d0d   Thumb Code    32  motor_mid.o(.text.motor_readdistance)
    motor_speed                              0x00002d31   Thumb Code    76  motor_mid.o(.text.motor_speed)
    motor_stop                               0x00002d81   Thumb Code    20  motor_mid.o(.text.motor_stop)
    motor_sync                               0x00002d99   Thumb Code    20  motor_mid.o(.text.motor_sync)
    move_to_pixel                            0x00002db5   Thumb Code    50  motor_mid.o(.text.move_to_pixel)
    my_strlen                                0x00002de7   Thumb Code    46  usart_mid.o(.text.my_strlen)
    pid_calculate_positional                 0x00002e15   Thumb Code    34  pid_mid.o(.text.pid_calculate_positional)
    pid_init                                 0x00002ebb   Thumb Code    80  pid_mid.o(.text.pid_init)
    pid_set_target                           0x00002f53   Thumb Code    16  pid_mid.o(.text.pid_set_target)
    pid_target                               0x00002f65   Thumb Code    56  motor_app.o(.text.pid_target)
    pixel_to_angle_x                         0x00002fad   Thumb Code    80  motor_mid.o(.text.pixel_to_angle_x)
    pixel_to_angle_y                         0x00003011   Thumb Code    80  motor_mid.o(.text.pixel_to_angle_y)
    read_place                               0x0000306d   Thumb Code    24  motor_app.o(.text.read_place)
    scheduler_init                           0x0000308d   Thumb Code     8  scheduler.o(.text.scheduler_init)
    scheduler_run                            0x00003095   Thumb Code   104  scheduler.o(.text.scheduler_run)
    sine_wave_calculate_point                0x00003105   Thumb Code   216  sine_wave.o(.text.sine_wave_calculate_point)
    sine_wave_pid_control                    0x000031e5   Thumb Code    12  sine_wave.o(.text.sine_wave_pid_control)
    sine_wave_task                           0x000031f5   Thumb Code   136  sine_wave.o(.text.sine_wave_task)
    triangle_pid_control                     0x00003295   Thumb Code    12  sine_wave.o(.text.triangle_pid_control)
    triangle_task                            0x000032a5   Thumb Code   148  sine_wave.o(.text.triangle_task)
    uart_printf                              0x00003351   Thumb Code   564  usart_mid.o(.text.uart_printf)
    uart_send_char                           0x00003585   Thumb Code    40  usart_mid.o(.text.uart_send_char)
    uart_send_data                           0x000035ad   Thumb Code    52  usart_mid.o(.text.uart_send_data)
    uart_send_string                         0x000035e1   Thumb Code    50  usart_mid.o(.text.uart_send_string)
    uint_to_hex_string                       0x00003615   Thumb Code   168  usart_mid.o(.text.uint_to_hex_string)
    unified_motion_task                      0x000036c5   Thumb Code    80  sine_wave.o(.text.unified_motion_task)
    vision_packet_sliding_parse              0x0000372d   Thumb Code   700  usart_mid.o(.text.vision_packet_sliding_parse)
    __aeabi_uidiv                            0x000039f5   Thumb Code    68  aeabi_sdivfast.o(.text_divfast)
    __aeabi_idiv                             0x00003a39   Thumb Code   434  aeabi_sdivfast.o(.text_divfast)
    __ARM_common_ll_muluu                    0x00003beb   Thumb Code    50  rredf.o(i.__ARM_common_ll_muluu)
    __ARM_fpclassifyf                        0x00003c1d   Thumb Code    34  fpclassifyf.o(i.__ARM_fpclassifyf)
    __mathlib_flt_infnan                     0x00003c3f   Thumb Code    10  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x00003c49   Thumb Code    12  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x00003c55   Thumb Code    14  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x00003c65   Thumb Code   366  rredf.o(i.__mathlib_rredf2)
    __aeabi_fcmpge                           0x00003de5   Thumb Code     0  fcmp.o(i._fgeq)
    _fgeq                                    0x00003de5   Thumb Code    22  fcmp.o(i._fgeq)
    __aeabi_fcmpgt                           0x00003dfb   Thumb Code     0  fcmp.o(i._fgr)
    _fgr                                     0x00003dfb   Thumb Code    22  fcmp.o(i._fgr)
    __aeabi_fcmple                           0x00003e11   Thumb Code     0  fcmp.o(i._fleq)
    _fleq                                    0x00003e11   Thumb Code    26  fcmp.o(i._fleq)
    __aeabi_fcmplt                           0x00003e2b   Thumb Code     0  fcmp.o(i._fls)
    _fls                                     0x00003e2b   Thumb Code    22  fcmp.o(i._fls)
    sinf                                     0x00003e41   Thumb Code   330  sinf.o(i.sinf)
    __aeabi_fadd                             0x00003fc5   Thumb Code     0  faddsub.o(x$fpl$fadd)
    _fadd                                    0x00003fc5   Thumb Code   134  faddsub.o(x$fpl$fadd)
    _fcmpge                                  0x00004051   Thumb Code    78  fgef.o(x$fpl$fgeqf)
    __aeabi_cfcmple                          0x000040a5   Thumb Code     0  flef.o(x$fpl$fleqf)
    _fcmple                                  0x000040a5   Thumb Code    78  flef.o(x$fpl$fleqf)
    __aeabi_fmul                             0x000040f9   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x000040f9   Thumb Code   172  fmul.o(x$fpl$fmul)
    __aeabi_frsub                            0x000041a9   Thumb Code     0  faddsub.o(x$fpl$frsb)
    _frsb                                    0x000041a9   Thumb Code    24  faddsub.o(x$fpl$frsb)
    __aeabi_fsub                             0x000041c1   Thumb Code     0  faddsub.o(x$fpl$fsub)
    _fsub                                    0x000041c1   Thumb Code   204  faddsub.o(x$fpl$fsub)
    __I$use$fp                               0x00004290   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x00004920   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00004940   Number         0  anon$$obj.o(Region$$Table)
    RE                                       0x20200000   Data           6  motor_mid.o(.data.RE)
    READ                                     0x20200006   Data           3  motor_mid.o(.data.READ)
    SE                                       0x20200009   Data          13  motor_mid.o(.data.SE)
    SE1                                      0x20200016   Data           8  motor_mid.o(.data.SE1)
    STP                                      0x2020001e   Data           5  motor_mid.o(.data.STP)
    TB                                       0x20200023   Data           4  motor_mid.o(.data.TB)
    motion_mode                              0x20200027   Data           1  sine_wave.o(.data.motion_mode)
    pid_params_x                             0x20200028   Data          36  motor_app.o(.data.pid_params_x)
    pid_params_y                             0x2020004c   Data          36  motor_app.o(.data.pid_params_y)
    send_count                               0x202000c4   Data           1  motor_app.o(.data.send_count)
    state_data                               0x202000e0   Data           1  task_func.o(.data.state_data)
    target_x                                 0x202000e4   Data           4  motor_app.o(.data.target_x)
    target_y                                 0x202000e8   Data           4  motor_app.o(.data.target_y)
    __libspace_start                         0x202000f0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20200150   Data           0  libspace.o(.bss)
    Key_Down                                 0x20200154   Data           1  key_app.o(.bss.Key_Down)
    Key_Old                                  0x20200155   Data           1  key_app.o(.bss.Key_Old)
    Key_Up                                   0x20200156   Data           1  key_app.o(.bss.Key_Up)
    Key_Val                                  0x20200157   Data           1  key_app.o(.bss.Key_Val)
    Motor_Cur_Pos                            0x20200158   Data           8  usart_mid.o(.bss.Motor_Cur_Pos)
    control_mode                             0x20200160   Data           1  task_func.o(.bss.control_mode)
    current_x                                0x20200164   Data           4  motor_app.o(.bss.current_x)
    current_y                                0x20200168   Data           4  motor_app.o(.bss.current_y)
    gCheckUART                               0x2020016d   Data           1  usart_mid.o(.bss.gCheckUART)
    gMCAN0Backup                             0x20200170   Data         152  ti_msp_dl_config.o(.bss.gMCAN0Backup)
    gUART_2Backup                            0x20200208   Data          48  ti_msp_dl_config.o(.bss.gUART_2Backup)
    gUart1Handle                             0x20200238   Data          76  usart_mid.o(.bss.gUart1Handle)
    gUart2Handle                             0x20200284   Data          76  usart_mid.o(.bss.gUart2Handle)
    gUartDma                                 0x202002d0   Data          31  usart_mid.o(.bss.gUartDma)
    motor_x                                  0x20200372   Data           1  motor_app.o(.bss.motor_x)
    motor_y                                  0x20200373   Data           1  motor_app.o(.bss.motor_y)
    open                                     0x20200374   Data           1  task_func.o(.bss.open)
    pid_x                                    0x20200378   Data          60  motor_app.o(.bss.pid_x)
    pid_y                                    0x202003b4   Data          60  motor_app.o(.bss.pid_y)
    rectangle_parsed                         0x202003f0   Data           1  usart_mid.o(.bss.rectangle_parsed)
    sine_direction                           0x202003f1   Data           1  sine_wave.o(.bss.sine_direction)
    sine_points_count                        0x202003f2   Data           2  sine_wave.o(.bss.sine_points_count)
    sine_wave_mode                           0x202003f4   Data           1  sine_wave.o(.bss.sine_wave_mode)
    system_flag                              0x20200400   Data           1  motor_app.o(.bss.system_flag)
    task_num                                 0x20200401   Data           1  scheduler.o(.bss.task_num)
    triangle_mode                            0x20200408   Data           1  sine_wave.o(.bss.triangle_mode)
    triangle_point_index                     0x20200409   Data           1  sine_wave.o(.bss.triangle_point_index)
    triangle_points                          0x2020040a   Data          12  sine_wave.o(.bss.triangle_points)
    uart_data                                0x2020041c   Data           4  usart_mid.o(.bss.uart_data)
    xy                                       0x202004a6   Data          20  motor_app.o(.bss.xy)
    xy_z                                     0x202004ba   Data          20  key_app.o(.bss.xy_z)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00004a30, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00004940, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           12    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          905  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO         1185    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x0000001a   Code   RO         1189    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0000013a   0x0000013a   0x00000006   PAD
    0x00000140   0x00000140   0x00000002   Code   RO         1186    !!handler_null      c_p.l(__scatter.o)
    0x00000142   0x00000142   0x00000006   PAD
    0x00000148   0x00000148   0x0000001c   Code   RO         1191    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000164   0x00000164   0x00000002   Code   RO         1043    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1058    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1060    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1062    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1065    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1067    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1069    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1072    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1074    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1076    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1078    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1080    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1082    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1084    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1086    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1088    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1090    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1092    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1096    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1098    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1100    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1102    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000002   Code   RO         1103    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000168   0x00000168   0x00000002   Code   RO         1140    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO         1168    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO         1170    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO         1173    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO         1176    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO         1178    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO         1181    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000002   Code   RO         1182    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000016c   0x0000016c   0x00000000   Code   RO          963    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000016c   0x0000016c   0x00000000   Code   RO         1001    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000016c   0x0000016c   0x00000006   Code   RO         1013    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000172   0x00000172   0x00000000   Code   RO         1003    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000172   0x00000172   0x00000004   Code   RO         1004    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000176   0x00000176   0x00000000   Code   RO         1006    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000176   0x00000176   0x00000008   Code   RO         1007    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000017e   0x0000017e   0x00000002   Code   RO         1049    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1111    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000180   0x00000180   0x00000004   Code   RO         1112    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000184   0x00000184   0x00000006   Code   RO         1113    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000018a   0x0000018a   0x00000002   PAD
    0x0000018c   0x0000018c   0x0000002c   Code   RO           13    .text               startup_mspm0g350x_uvision.o
    0x000001b8   0x000001b8   0x000001f8   Code   RO          895    .text               c_p.l(aeabi_sdivfast.o)
    0x000003b0   0x000003b0   0x00000006   Code   RO          903    .text               c_p.l(heapauxi.o)
    0x000003b6   0x000003b6   0x00000002   PAD
    0x000003b8   0x000003b8   0x0000007c   Code   RO          907    .text               fz_ps.l(d2f.o)
    0x00000434   0x00000434   0x00000054   Code   RO          916    .text               fz_ps.l(f2d.o)
    0x00000488   0x00000488   0x00000160   Code   RO          948    .text               fz_ps.l(fdiv.o)
    0x000005e8   0x000005e8   0x0000004c   Code   RO          951    .text               fz_ps.l(ffixi.o)
    0x00000634   0x00000634   0x00000030   Code   RO          953    .text               fz_ps.l(ffixui.o)
    0x00000664   0x00000664   0x0000005e   Code   RO          955    .text               fz_ps.l(fflti.o)
    0x000006c2   0x000006c2   0x00000016   Code   RO          968    .text               c_p.l(_rserrno.o)
    0x000006d8   0x000006d8   0x00000080   Code   RO          976    .text               fz_ps.l(frnd.o)
    0x00000758   0x00000758   0x00000008   Code   RO         1020    .text               c_p.l(rt_errno_addr_intlibspace.o)
    0x00000760   0x00000760   0x00000064   Code   RO         1022    .text               fz_ps.l(fcmpin.o)
    0x000007c4   0x000007c4   0x00000044   Code   RO         1024    .text               fz_ps.l(fscalbn.o)
    0x00000808   0x00000808   0x00000008   Code   RO         1026    .text               c_p.l(libspace.o)
    0x00000810   0x00000810   0x0000003e   Code   RO         1029    .text               c_p.l(sys_stackheap_outer.o)
    0x0000084e   0x0000084e   0x00000010   Code   RO         1032    .text               c_p.l(exit.o)
    0x0000085e   0x0000085e   0x0000002e   Code   RO         1044    .text               fz_ps.l(cmpret.o)
    0x0000088c   0x0000088c   0x00000010   Code   RO         1046    .text               fz_ps.l(fnan2.o)
    0x0000089c   0x0000089c   0x0000005e   Code   RO         1104    .text               fz_ps.l(retnan.o)
    0x000008fa   0x000008fa   0x00000002   PAD
    0x000008fc   0x000008fc   0x0000000c   Code   RO         1106    .text               c_p.l(sys_exit.o)
    0x00000908   0x00000908   0x00000002   Code   RO         1129    .text               c_p.l(use_no_semi.o)
    0x0000090a   0x0000090a   0x00000000   Code   RO         1131    .text               c_p.l(indicate_semi.o)
    0x0000090a   0x0000090a   0x0000000a   Code   RO          538    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00000914   0x00000914   0x00000028   Code   RO          126    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x0000093c   0x0000093c   0x00000026   Code   RO          461    .text.DL_DMA_enableChannel  usart_mid.o
    0x00000962   0x00000962   0x00000002   PAD
    0x00000964   0x00000964   0x00000044   Code   RO          547    .text.DL_DMA_initChannel  driverlib.a(dl_dma.o)
    0x000009a8   0x000009a8   0x00000028   Code   RO          457    .text.DL_DMA_setDestAddr  usart_mid.o
    0x000009d0   0x000009d0   0x00000028   Code   RO          455    .text.DL_DMA_setSrcAddr  usart_mid.o
    0x000009f8   0x000009f8   0x00000030   Code   RO          459    .text.DL_DMA_setTransferSize  usart_mid.o
    0x00000a28   0x00000a28   0x00000014   Code   RO           76    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x00000a3c   0x00000a3c   0x00000018   Code   RO           74    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00000a54   0x00000a54   0x00000014   Code   RO           54    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00000a68   0x00000a68   0x00000030   Code   RO           70    .text.DL_GPIO_initDigitalInputFeatures  ti_msp_dl_config.o
    0x00000a98   0x00000a98   0x00000014   Code   RO           68    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x00000aac   0x00000aac   0x00000014   Code   RO           62    .text.DL_GPIO_initPeripheralAnalogFunction  ti_msp_dl_config.o
    0x00000ac0   0x00000ac0   0x0000001c   Code   RO           66    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x00000adc   0x00000adc   0x0000001c   Code   RO           64    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x00000af8   0x00000af8   0x00000016   Code   RO          158    .text.DL_GPIO_readPins  key_app.o
    0x00000b0e   0x00000b0e   0x00000002   PAD
    0x00000b10   0x00000b10   0x00000018   Code   RO           46    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00000b28   0x00000b28   0x00000018   Code   RO           72    .text.DL_GPIO_setPins  ti_msp_dl_config.o
    0x00000b40   0x00000b40   0x00000018   Code   RO          122    .text.DL_MCAN_clearInterruptStatus  ti_msp_dl_config.o
    0x00000b58   0x00000b58   0x0000001c   Code   RO          124    .text.DL_MCAN_enableInterrupt  ti_msp_dl_config.o
    0x00000b74   0x00000b74   0x0000001c   Code   RO          607    .text.DL_MCAN_enableIntr  driverlib.a(dl_mcan.o)
    0x00000b90   0x00000b90   0x0000001c   Code   RO          613    .text.DL_MCAN_enableIntrLine  driverlib.a(dl_mcan.o)
    0x00000bac   0x00000bac   0x00000012   Code   RO          120    .text.DL_MCAN_enableModuleClock  ti_msp_dl_config.o
    0x00000bbe   0x00000bbe   0x00000002   PAD
    0x00000bc0   0x00000bc0   0x00000018   Code   RO           60    .text.DL_MCAN_enablePower  ti_msp_dl_config.o
    0x00000bd8   0x00000bd8   0x0000000c   Code   RO          571    .text.DL_MCAN_getOpMode  driverlib.a(dl_mcan.o)
    0x00000be4   0x00000be4   0x00000044   Code   RO          669    .text.DL_MCAN_getRevisionId  driverlib.a(dl_mcan.o)
    0x00000c28   0x00000c28   0x000000d8   Code   RO          573    .text.DL_MCAN_init  driverlib.a(dl_mcan.o)
    0x00000d00   0x00000d00   0x00000010   Code   RO          567    .text.DL_MCAN_isMemInitDone  driverlib.a(dl_mcan.o)
    0x00000d10   0x00000d10   0x00000218   Code   RO          581    .text.DL_MCAN_msgRAMConfig  driverlib.a(dl_mcan.o)
    0x00000f28   0x00000f28   0x00000018   Code   RO           52    .text.DL_MCAN_reset  ti_msp_dl_config.o
    0x00000f40   0x00000f40   0x0000001c   Code   RO          609    .text.DL_MCAN_selectIntrLine  driverlib.a(dl_mcan.o)
    0x00000f5c   0x00000f5c   0x000000e0   Code   RO          579    .text.DL_MCAN_setBitTime  driverlib.a(dl_mcan.o)
    0x0000103c   0x0000103c   0x00000020   Code   RO          559    .text.DL_MCAN_setClockConfig  driverlib.a(dl_mcan.o)
    0x0000105c   0x0000105c   0x00000030   Code   RO          583    .text.DL_MCAN_setExtIDAndMask  driverlib.a(dl_mcan.o)
    0x0000108c   0x0000108c   0x00000018   Code   RO          569    .text.DL_MCAN_setOpMode  driverlib.a(dl_mcan.o)
    0x000010a4   0x000010a4   0x000000c0   Code   RO          858    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00001164   0x00001164   0x0000000c   Code   RO           82    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00001170   0x00001170   0x00000014   Code   RO           84    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x00001184   0x00001184   0x00000014   Code   RO           86    .text.DL_SYSCTL_enableMFCLK  ti_msp_dl_config.o
    0x00001198   0x00001198   0x00000010   Code   RO           88    .text.DL_SYSCTL_enableMFPCLK  ti_msp_dl_config.o
    0x000011a8   0x000011a8   0x00000018   Code   RO           78    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x000011c0   0x000011c0   0x00000054   Code   RO          872    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00001214   0x00001214   0x00000020   Code   RO           90    .text.DL_SYSCTL_setMFPCLKSource  ti_msp_dl_config.o
    0x00001234   0x00001234   0x0000001c   Code   RO           80    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x00001250   0x00001250   0x00000010   Code   RO           94    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x00001260   0x00001260   0x00000018   Code   RO           92    .text.DL_Timer_enableInterrupt  ti_msp_dl_config.o
    0x00001278   0x00001278   0x00000014   Code   RO           56    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x0000128c   0x0000128c   0x000000f0   Code   RO          721    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x0000137c   0x0000137c   0x00000010   Code   RO           48    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x0000138c   0x0000138c   0x00000018   Code   RO          717    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x000013a4   0x000013a4   0x00000016   Code   RO          110    .text.DL_UART_enable  ti_msp_dl_config.o
    0x000013ba   0x000013ba   0x00000002   PAD
    0x000013bc   0x000013bc   0x00000018   Code   RO          102    .text.DL_UART_enableDMAReceiveEvent  ti_msp_dl_config.o
    0x000013d4   0x000013d4   0x00000018   Code   RO          104    .text.DL_UART_enableFIFOs  ti_msp_dl_config.o
    0x000013ec   0x000013ec   0x00000018   Code   RO          100    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x00001404   0x00001404   0x0000001c   Code   RO          483    .text.DL_UART_enableInterrupt  usart_mid.o
    0x00001420   0x00001420   0x00000018   Code   RO           58    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x00001438   0x00001438   0x00000012   Code   RO          503    .text.DL_UART_getPendingInterrupt  usart_mid.o
    0x0000144a   0x0000144a   0x00000002   PAD
    0x0000144c   0x0000144c   0x00000048   Code   RO          818    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00001494   0x00001494   0x00000018   Code   RO          431    .text.DL_UART_isBusy  usart_mid.o
    0x000014ac   0x000014ac   0x00000014   Code   RO          507    .text.DL_UART_receiveData  usart_mid.o
    0x000014c0   0x000014c0   0x00000018   Code   RO           50    .text.DL_UART_reset  ti_msp_dl_config.o
    0x000014d8   0x000014d8   0x0000004c   Code   RO           98    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x00001524   0x00001524   0x00000012   Code   RO          820    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x00001536   0x00001536   0x0000001e   Code   RO           96    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x00001554   0x00001554   0x00000024   Code   RO          106    .text.DL_UART_setRXFIFOThreshold  ti_msp_dl_config.o
    0x00001578   0x00001578   0x00000028   Code   RO          108    .text.DL_UART_setTXFIFOThreshold  ti_msp_dl_config.o
    0x000015a0   0x000015a0   0x00000016   Code   RO          433    .text.DL_UART_transmitData  usart_mid.o
    0x000015b6   0x000015b6   0x00000002   PAD
    0x000015b8   0x000015b8   0x0000007c   Code   RO          330    .text.GetTick       scheduler.o
    0x00001634   0x00001634   0x000000ac   Code   RO          162    .text.Key_Proc      key_app.o
    0x000016e0   0x000016e0   0x00000074   Code   RO          156    .text.Key_Read      key_app.o
    0x00001754   0x00001754   0x00000112   Code   RO          240    .text.Motor_Set_Speed  motor_mid.o
    0x00001866   0x00001866   0x00000002   PAD
    0x00001868   0x00001868   0x00000014   Code   RO          112    .text.SYSCFG_DL_DMA_CH0_init  ti_msp_dl_config.o
    0x0000187c   0x0000187c   0x00000014   Code   RO          116    .text.SYSCFG_DL_DMA_CH1_init  ti_msp_dl_config.o
    0x00001890   0x00001890   0x00000018   Code   RO          114    .text.SYSCFG_DL_DMA_CH2_init  ti_msp_dl_config.o
    0x000018a8   0x000018a8   0x00000010   Code   RO           36    .text.SYSCFG_DL_DMA_init  ti_msp_dl_config.o
    0x000018b8   0x000018b8   0x000000fc   Code   RO           24    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x000019b4   0x000019b4   0x000000d4   Code   RO           40    .text.SYSCFG_DL_MCAN0_init  ti_msp_dl_config.o
    0x00001a88   0x00001a88   0x00000044   Code   RO           26    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001acc   0x00001acc   0x0000000c   Code   RO           38    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001ad8   0x00001ad8   0x00000030   Code   RO           28    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00001b08   0x00001b08   0x00000068   Code   RO           30    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00001b70   0x00001b70   0x00000050   Code   RO           32    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x00001bc0   0x00001bc0   0x00000068   Code   RO           34    .text.SYSCFG_DL_UART_2_init  ti_msp_dl_config.o
    0x00001c28   0x00001c28   0x00000044   Code   RO           20    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00001c6c   0x00001c6c   0x00000084   Code   RO           22    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00001cf0   0x00001cf0   0x00000050   Code   RO          118    .text.SysTick_Config  ti_msp_dl_config.o
    0x00001d40   0x00001d40   0x00000014   Code   RO          328    .text.SysTick_Handler  scheduler.o
    0x00001d54   0x00001d54   0x0000000c   Code   RO          326    .text.SystemTime_Init  scheduler.o
    0x00001d60   0x00001d60   0x00000024   Code   RO          501    .text.UART0_IRQHandler  usart_mid.o
    0x00001d84   0x00001d84   0x00000070   Code   RO          485    .text.UART1_CheckTimeout  usart_mid.o
    0x00001df4   0x00001df4   0x00000070   Code   RO          505    .text.UART1_IRQHandler  usart_mid.o
    0x00001e64   0x00001e64   0x00000068   Code   RO          489    .text.UART1_ProcessData  usart_mid.o
    0x00001ecc   0x00001ecc   0x0000005c   Code   RO          465    .text.UART1_Timeout_Init  usart_mid.o
    0x00001f28   0x00001f28   0x00000054   Code   RO          495    .text.UART2_CheckTimeout  usart_mid.o
    0x00001f7c   0x00001f7c   0x0000005c   Code   RO          497    .text.UART2_ProcessData  usart_mid.o
    0x00001fd8   0x00001fd8   0x00000054   Code   RO          467    .text.UART2_Timeout_Init  usart_mid.o
    0x0000202c   0x0000202c   0x0000007c   Code   RO          509    .text.UART3_IRQHandler  usart_mid.o
    0x000020a8   0x000020a8   0x00000054   Code   RO          453    .text.UART_DMA_Init  usart_mid.o
    0x000020fc   0x000020fc   0x0000002c   Code   RO          463    .text.__NVIC_EnableIRQ  usart_mid.o
    0x00002128   0x00002128   0x00000084   Code   RO          128    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x000021ac   0x000021ac   0x000001fc   Code   RO          193    .text.app_pid_calc  motor_app.o
    0x000023a8   0x000023a8   0x00000048   Code   RO          183    .text.app_pid_init  motor_app.o
    0x000023f0   0x000023f0   0x00000040   Code   RO          292    .text.app_pid_limit_integral  pid_mid.o
    0x00002430   0x00002430   0x00000164   Code   RO          355    .text.common_pid_control  sine_wave.o
    0x00002594   0x00002594   0x00000048   Code   RO          246    .text.constrain_angle  motor_mid.o
    0x000025dc   0x000025dc   0x0000003c   Code   RO          415    .text.contorol_Task  task_func.o
    0x00002618   0x00002618   0x00000024   Code   RO          336    .text.delay_ms      scheduler.o
    0x0000263c   0x0000263c   0x00000030   Code   RO          242    .text.dis_read      motor_mid.o
    0x0000266c   0x0000266c   0x00000070   Code   RO          160    .text.example_pixel_coordinate_control  key_app.o
    0x000026dc   0x000026dc   0x000000e0   Code   RO          447    .text.float_to_string  usart_mid.o
    0x000027bc   0x000027bc   0x00000094   Code   RO          197    .text.get_data      motor_app.o
    0x00002850   0x00002850   0x000000c6   Code   RO          443    .text.int_to_string  usart_mid.o
    0x00002916   0x00002916   0x00000002   PAD
    0x00002918   0x00002918   0x00000070   Code   RO          353    .text.is_target_reached  sine_wave.o
    0x00002988   0x00002988   0x00000028   Code   RO            2    .text.main          empty.o
    0x000029b0   0x000029b0   0x00000074   Code   RO          226    .text.motor_distance  motor_mid.o
    0x00002a24   0x00002a24   0x00000040   Code   RO          222    .text.motor_init    motor_mid.o
    0x00002a64   0x00002a64   0x00000114   Code   RO          256    .text.motor_move_angle  motor_mid.o
    0x00002b78   0x00002b78   0x00000194   Code   RO          491    .text.motor_packet_sliding_parse  usart_mid.o
    0x00002d0c   0x00002d0c   0x00000024   Code   RO          230    .text.motor_readdistance  motor_mid.o
    0x00002d30   0x00002d30   0x00000050   Code   RO          228    .text.motor_speed   motor_mid.o
    0x00002d80   0x00002d80   0x00000018   Code   RO          224    .text.motor_stop    motor_mid.o
    0x00002d98   0x00002d98   0x0000001c   Code   RO          238    .text.motor_sync    motor_mid.o
    0x00002db4   0x00002db4   0x00000032   Code   RO          254    .text.move_to_pixel  motor_mid.o
    0x00002de6   0x00002de6   0x0000002e   Code   RO          441    .text.my_strlen     usart_mid.o
    0x00002e14   0x00002e14   0x00000022   Code   RO          304    .text.pid_calculate_positional  pid_mid.o
    0x00002e36   0x00002e36   0x00000084   Code   RO          306    .text.pid_formula_positional  pid_mid.o
    0x00002eba   0x00002eba   0x00000050   Code   RO          294    .text.pid_init      pid_mid.o
    0x00002f0a   0x00002f0a   0x00000048   Code   RO          308    .text.pid_out_limit  pid_mid.o
    0x00002f52   0x00002f52   0x00000010   Code   RO          296    .text.pid_set_target  pid_mid.o
    0x00002f62   0x00002f62   0x00000002   PAD
    0x00002f64   0x00002f64   0x00000048   Code   RO          191    .text.pid_target    motor_app.o
    0x00002fac   0x00002fac   0x00000064   Code   RO          250    .text.pixel_to_angle_x  motor_mid.o
    0x00003010   0x00003010   0x0000005c   Code   RO          252    .text.pixel_to_angle_y  motor_mid.o
    0x0000306c   0x0000306c   0x00000020   Code   RO          189    .text.read_place    motor_app.o
    0x0000308c   0x0000308c   0x00000008   Code   RO          332    .text.scheduler_init  scheduler.o
    0x00003094   0x00003094   0x00000070   Code   RO          334    .text.scheduler_run  scheduler.o
    0x00003104   0x00003104   0x000000e0   Code   RO          367    .text.sine_wave_calculate_point  sine_wave.o
    0x000031e4   0x000031e4   0x00000010   Code   RO          373    .text.sine_wave_pid_control  sine_wave.o
    0x000031f4   0x000031f4   0x000000a0   Code   RO          371    .text.sine_wave_task  sine_wave.o
    0x00003294   0x00003294   0x00000010   Code   RO          387    .text.triangle_pid_control  sine_wave.o
    0x000032a4   0x000032a4   0x000000ac   Code   RO          385    .text.triangle_task  sine_wave.o
    0x00003350   0x00003350   0x00000234   Code   RO          449    .text.uart_printf   usart_mid.o
    0x00003584   0x00003584   0x00000028   Code   RO          429    .text.uart_send_char  usart_mid.o
    0x000035ac   0x000035ac   0x00000034   Code   RO          437    .text.uart_send_data  usart_mid.o
    0x000035e0   0x000035e0   0x00000032   Code   RO          435    .text.uart_send_string  usart_mid.o
    0x00003612   0x00003612   0x00000002   PAD
    0x00003614   0x00003614   0x000000b0   Code   RO          445    .text.uint_to_hex_string  usart_mid.o
    0x000036c4   0x000036c4   0x00000068   Code   RO          389    .text.unified_motion_task  sine_wave.o
    0x0000372c   0x0000372c   0x000002c8   Code   RO          499    .text.vision_packet_sliding_parse  usart_mid.o
    0x000039f4   0x000039f4   0x000001f6   Code   RO          896    .text_divfast       c_p.l(aeabi_sdivfast.o)
    0x00003bea   0x00003bea   0x00000032   Code   RO          998    i.__ARM_common_ll_muluu  m_ps.l(rredf.o)
    0x00003c1c   0x00003c1c   0x00000022   Code   RO          979    i.__ARM_fpclassifyf  m_ps.l(fpclassifyf.o)
    0x00003c3e   0x00003c3e   0x0000000a   Code   RO          982    i.__mathlib_flt_infnan  m_ps.l(funder.o)
    0x00003c48   0x00003c48   0x0000000c   Code   RO          984    i.__mathlib_flt_invalid  m_ps.l(funder.o)
    0x00003c54   0x00003c54   0x0000000e   Code   RO          987    i.__mathlib_flt_underflow  m_ps.l(funder.o)
    0x00003c62   0x00003c62   0x00000002   PAD
    0x00003c64   0x00003c64   0x00000180   Code   RO          995    i.__mathlib_rredf2  m_ps.l(rredf.o)
    0x00003de4   0x00003de4   0x00000016   Code   RO          931    i._fgeq             fz_ps.l(fcmp.o)
    0x00003dfa   0x00003dfa   0x00000016   Code   RO          932    i._fgr              fz_ps.l(fcmp.o)
    0x00003e10   0x00003e10   0x0000001a   Code   RO          933    i._fleq             fz_ps.l(fcmp.o)
    0x00003e2a   0x00003e2a   0x00000016   Code   RO          934    i._fls              fz_ps.l(fcmp.o)
    0x00003e40   0x00003e40   0x00000184   Code   RO          960    i.sinf              m_ps.l(sinf.o)
    0x00003fc4   0x00003fc4   0x0000008c   Code   RO          918    x$fpl$fadd          fz_ps.l(faddsub.o)
    0x00004050   0x00004050   0x00000054   Code   RO          972    x$fpl$fgeqf         fz_ps.l(fgef.o)
    0x000040a4   0x000040a4   0x00000054   Code   RO          974    x$fpl$fleqf         fz_ps.l(flef.o)
    0x000040f8   0x000040f8   0x000000b0   Code   RO          957    x$fpl$fmul          fz_ps.l(fmul.o)
    0x000041a8   0x000041a8   0x00000018   Code   RO          919    x$fpl$frsb          fz_ps.l(faddsub.o)
    0x000041c0   0x000041c0   0x000000d0   Code   RO          920    x$fpl$fsub          fz_ps.l(faddsub.o)
    0x00004290   0x00004290   0x00000000   Code   RO          978    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x00004290   0x00004290   0x00000040   Data   RO          949    .constdata          fz_ps.l(fdiv.o)
    0x000042d0   0x000042d0   0x00000020   Data   RO          996    .constdata          m_ps.l(rredf.o)
    0x000042f0   0x000042f0   0x00000018   Data   RO          141    .rodata.gDMA_CH0Config  ti_msp_dl_config.o
    0x00004308   0x00004308   0x00000018   Data   RO          143    .rodata.gDMA_CH1Config  ti_msp_dl_config.o
    0x00004320   0x00004320   0x00000018   Data   RO          142    .rodata.gDMA_CH2Config  ti_msp_dl_config.o
    0x00004338   0x00004338   0x00000020   Data   RO          146    .rodata.gMCAN0BitTimes  ti_msp_dl_config.o
    0x00004358   0x00004358   0x00000004   Data   RO          144    .rodata.gMCAN0ClockConf  ti_msp_dl_config.o
    0x0000435c   0x0000435c   0x00000034   Data   RO          145    .rodata.gMCAN0InitParams  ti_msp_dl_config.o
    0x00004390   0x00004390   0x00000060   Data   RO          147    .rodata.gMCAN0MsgRAMConfigParams  ti_msp_dl_config.o
    0x000043f0   0x000043f0   0x00000028   Data   RO          132    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00004418   0x00004418   0x00000003   Data   RO          133    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x0000441b   0x0000441b   0x00000001   PAD
    0x0000441c   0x0000441c   0x00000014   Data   RO          134    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00004430   0x00004430   0x00000002   Data   RO          135    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00004432   0x00004432   0x0000000a   Data   RO          136    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x0000443c   0x0000443c   0x00000002   Data   RO          137    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x0000443e   0x0000443e   0x0000000a   Data   RO          138    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x00004448   0x00004448   0x00000002   Data   RO          139    .rodata.gUART_2ClockConfig  ti_msp_dl_config.o
    0x0000444a   0x0000444a   0x0000000a   Data   RO          140    .rodata.gUART_2Config  ti_msp_dl_config.o
    0x00004454   0x00004454   0x00000088   Data   RO          171    .rodata.str1.1      key_app.o
    0x000044dc   0x000044dc   0x0000002b   Data   RO          212    .rodata.str1.1      motor_app.o
    0x00004507   0x00004507   0x000003f0   Data   RO          404    .rodata.str1.1      sine_wave.o
    0x000048f7   0x000048f7   0x00000022   Data   RO          517    .rodata.str1.1      usart_mid.o
    0x00004919   0x00004919   0x00000007   PAD
    0x00004920   0x00004920   0x00000020   Data   RO         1184    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00004940, Size: 0x000005d0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00004940   0x00000006   Data   RW          270    .data.RE            motor_mid.o
    0x20200006   0x00004946   0x00000003   Data   RW          275    .data.READ          motor_mid.o
    0x20200009   0x00004949   0x0000000d   Data   RW          271    .data.SE            motor_mid.o
    0x20200016   0x00004956   0x00000008   Data   RW          272    .data.SE1           motor_mid.o
    0x2020001e   0x0000495e   0x00000005   Data   RW          274    .data.STP           motor_mid.o
    0x20200023   0x00004963   0x00000004   Data   RW          273    .data.TB            motor_mid.o
    0x20200027   0x00004967   0x00000001   Data   RW          403    .data.motion_mode   sine_wave.o
    0x20200028   0x00004968   0x00000024   Data   RW          206    .data.pid_params_x  motor_app.o
    0x2020004c   0x0000498c   0x00000024   Data   RW          207    .data.pid_params_y  motor_app.o
    0x20200070   0x000049b0   0x00000054   Data   RW          345    .data.scheduler_task  scheduler.o
    0x202000c4   0x00004a04   0x00000001   Data   RW          211    .data.send_count    motor_app.o
    0x202000c5   0x00004a05   0x00000003   PAD
    0x202000c8   0x00004a08   0x00000018   Data   RW          405    .data.sine_params   sine_wave.o
    0x202000e0   0x00004a20   0x00000001   Data   RW          421    .data.state_data    task_func.o
    0x202000e1   0x00004a21   0x00000003   PAD
    0x202000e4   0x00004a24   0x00000004   Data   RW          199    .data.target_x      motor_app.o
    0x202000e8   0x00004a28   0x00000004   Data   RW          200    .data.target_y      motor_app.o
    0x202000ec   0x00004a2c   0x00000004   PAD
    0x202000f0        -       0x00000060   Zero   RW         1027    .bss                c_p.l(libspace.o)
    0x20200150        -       0x00000004   Zero   RW          343    .bss.GetTick.last_time  scheduler.o
    0x20200154        -       0x00000001   Zero   RW          174    .bss.Key_Down       key_app.o
    0x20200155        -       0x00000001   Zero   RW          173    .bss.Key_Old        key_app.o
    0x20200156        -       0x00000001   Zero   RW          175    .bss.Key_Up         key_app.o
    0x20200157        -       0x00000001   Zero   RW          172    .bss.Key_Val        key_app.o
    0x20200158        -       0x00000008   Zero   RW          522    .bss.Motor_Cur_Pos  usart_mid.o
    0x20200160        -       0x00000001   Zero   RW          419    .bss.control_mode   task_func.o
    0x20200161   0x00004a2c   0x00000003   PAD
    0x20200164        -       0x00000004   Zero   RW          204    .bss.current_x      motor_app.o
    0x20200168        -       0x00000004   Zero   RW          205    .bss.current_y      motor_app.o
    0x2020016c        -       0x00000001   Zero   RW          279    .bss.dis_read.read_count  motor_mid.o
    0x2020016d        -       0x00000001   Zero   RW          513    .bss.gCheckUART     usart_mid.o
    0x2020016e   0x00004a2c   0x00000002   PAD
    0x20200170        -       0x00000098   Zero   RW          131    .bss.gMCAN0Backup   ti_msp_dl_config.o
    0x20200208        -       0x00000030   Zero   RW          130    .bss.gUART_2Backup  ti_msp_dl_config.o
    0x20200238        -       0x0000004c   Zero   RW          514    .bss.gUart1Handle   usart_mid.o
    0x20200284        -       0x0000004c   Zero   RW          515    .bss.gUart2Handle   usart_mid.o
    0x202002d0        -       0x0000001f   Zero   RW          512    .bss.gUartDma       usart_mid.o
    0x202002ef        -       0x00000080   Zero   RW          526    .bss.motor_packet_sliding_parse.rxBuffer  usart_mid.o
    0x2020036f   0x00004a2c   0x00000001   PAD
    0x20200370        -       0x00000002   Zero   RW          527    .bss.motor_packet_sliding_parse.rxCounter  usart_mid.o
    0x20200372        -       0x00000001   Zero   RW          213    .bss.motor_x        motor_app.o
    0x20200373        -       0x00000001   Zero   RW          214    .bss.motor_y        motor_app.o
    0x20200374        -       0x00000001   Zero   RW          420    .bss.open           task_func.o
    0x20200375   0x00004a2c   0x00000003   PAD
    0x20200378        -       0x0000003c   Zero   RW          208    .bss.pid_x          motor_app.o
    0x202003b4        -       0x0000003c   Zero   RW          209    .bss.pid_y          motor_app.o
    0x202003f0        -       0x00000001   Zero   RW          511    .bss.rectangle_parsed  usart_mid.o
    0x202003f1        -       0x00000001   Zero   RW          400    .bss.sine_direction  sine_wave.o
    0x202003f2        -       0x00000002   Zero   RW          399    .bss.sine_points_count  sine_wave.o
    0x202003f4        -       0x00000001   Zero   RW          397    .bss.sine_wave_mode  sine_wave.o
    0x202003f5   0x00004a2c   0x00000003   PAD
    0x202003f8        -       0x00000004   Zero   RW          340    .bss.systemTimeMs   scheduler.o
    0x202003fc        -       0x00000004   Zero   RW          341    .bss.systemTimeMs_backup  scheduler.o
    0x20200400        -       0x00000001   Zero   RW          210    .bss.system_flag    motor_app.o
    0x20200401        -       0x00000001   Zero   RW          344    .bss.task_num       scheduler.o
    0x20200402   0x00004a2c   0x00000002   PAD
    0x20200404        -       0x00000004   Zero   RW          342    .bss.time_error_count  scheduler.o
    0x20200408        -       0x00000001   Zero   RW          401    .bss.triangle_mode  sine_wave.o
    0x20200409        -       0x00000001   Zero   RW          402    .bss.triangle_point_index  sine_wave.o
    0x2020040a        -       0x0000000c   Zero   RW          406    .bss.triangle_points  sine_wave.o
    0x20200416   0x00004a2c   0x00000002   PAD
    0x20200418        -       0x00000004   Zero   RW          523    .bss.uart1_data_count  usart_mid.o
    0x2020041c        -       0x00000004   Zero   RW          516    .bss.uart_data      usart_mid.o
    0x20200420        -       0x00000004   Zero   RW          407    .bss.unified_motion_task.debug_count  sine_wave.o
    0x20200424        -       0x00000080   Zero   RW          524    .bss.vision_packet_sliding_parse.rxBuffer  usart_mid.o
    0x202004a4        -       0x00000002   Zero   RW          525    .bss.vision_packet_sliding_parse.rxCounter  usart_mid.o
    0x202004a6        -       0x00000014   Zero   RW          203    .bss.xy             motor_app.o
    0x202004ba        -       0x00000014   Zero   RW          168    .bss.xy_z           key_app.o
    0x202004ce   0x00004a2c   0x00000002   PAD
    0x202004d0        -       0x00000000   Zero   RW           11    HEAP                startup_mspm0g350x_uvision.o
    0x202004d0        -       0x00000100   Zero   RW           10    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        40          0          0          0          0        394   empty.o
       422         60        136          0         24       5832   key_app.o
       832         72         43         81        151       5029   motor_app.o
      1260         68          0         39          1       8536   motor_mid.o
       398          0          0          0          0       3291   pid_mid.o
       312         36          0         84         17       1942   scheduler.o
      1160        144       1008         25         22       8142   sine_wave.o
        44         18        192          0        256        692   startup_mspm0g350x_uvision.o
        60          8          0          1          2        783   task_func.o
      2338        240        355          0        200      33965   ti_msp_dl_config.o
      3712        120         34          0        461      17533   usart_mid.o

    ----------------------------------------------------------------------
     10598        <USER>       <GROUP>        236       1156      86139   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          8          6         22          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0         92   _rserrno.o
      1006          4          0          0          0        184   aeabi_sdivfast.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        803   dl_common.o
        68          4          0          0          0       4510   dl_dma.o
      1260         80          0          0          0      71919   dl_mcan.o
       276         20          0          0          0      12877   dl_sysctl_mspm0g1x0x_g3x0x.o
       264        156          0          0          0      41557   dl_timer.o
        90          8          0          0          0      14163   dl_uart.o
        46          0          0          0          0         60   cmpret.o
       124          4          0          0          0         72   d2f.o
        84          4          0          0          0         60   f2d.o
       372          8          0          0          0        240   faddsub.o
        92          0          0          0          0        272   fcmp.o
       100          4          0          0          0         68   fcmpin.o
       352         10         64          0          0         92   fdiv.o
        76          0          0          0          0         68   ffixi.o
        48          0          0          0          0         60   ffixui.o
        94          0          0          0          0         92   fflti.o
        84          4          0          0          0         76   fgef.o
        84          4          0          0          0         76   flef.o
       176          4          0          0          0         80   fmul.o
        16          6          0          0          0         68   fnan2.o
       128          6          0          0          0         68   frnd.o
        68          4          0          0          0         68   fscalbn.o
        94          0          0          0          0         68   retnan.o
         0          0          0          0          0          0   usenofp.o
        34          0          0          0          0         60   fpclassifyf.o
        36          0          0          0          0        204   funder.o
       434         18         32          0          0        168   rredf.o
       388         58          0          0          0        104   sinf.o

    ----------------------------------------------------------------------
      6250        <USER>         <GROUP>          0         96     148845   Library Totals
        24          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1328         26          0          0         96        892   c_p.l
      1968        268          0          0          0     145829   driverlib.a
      2038         58         64          0          0       1588   fz_ps.l
       892         76         32          0          0        536   m_ps.l

    ----------------------------------------------------------------------
      6250        <USER>         <GROUP>          0         96     148845   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     16848       1198       1904        236       1252     233136   Grand Totals
     16848       1198       1904        236       1252     233136   ELF Image Totals
     16848       1198       1904        236          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                18752 (  18.31kB)
    Total RW  Size (RW Data + ZI Data)              1488 (   1.45kB)
    Total ROM Size (Code + RO Data + RW Data)      18988 (  18.54kB)

==============================================================================

