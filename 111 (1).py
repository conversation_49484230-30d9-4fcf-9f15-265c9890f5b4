# version:2.0.3 - 修复坐标转换错误
import cv2
import numpy as np
import os
import matplotlib
import matplotlib.pyplot as plt
from datetime import datetime
from scipy.interpolate import splprep, splev
import math
from collections import deque

# 设置支持中文的字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False


# 全局变量用于存储参数和状态
class Config:
    def __init__(self):
        # 默认参数值
        self.canny_low = 50
        self.canny_high = 150
        self.median_blur_size = 15
        self.adaptive_block_size = 31
        self.adaptive_c = 7
        self.morph_open_iter = 1
        self.morph_close_iter = 2
        self.roi_x = 100
        self.roi_y = 100
        self.roi_width = 400
        self.roi_height = 300
        self.show_roi = True
        self.show_trajectory = True
        self.save_frames = False
        self.frame_counter = 0
        self.save_interval = 10  # 每10帧保存一次结果
        self.shrink_factor = 0.1  # 向内收缩的比例

        # 手动画框ROI相关参数
        self.manual_roi_mode = False  # 手动画框模式
        self.drawing = False  # 是否正在画框
        self.manual_roi_start = None  # 画框起始点
        self.manual_roi_end = None  # 画框结束点
        self.manual_roi_defined = False  # 是否已定义手动ROI

        # 阈值文件路径
        self.threshold_file = "curve_thresholds.txt"

        # 轨迹平滑参数
        self.trajectory_history = []  # 存储历史轨迹
        self.history_size = 5  # 历史帧数
        self.smooth_factor = 0.3  # 平滑系数 (0-1, 越小越平滑)


config = Config()


def detect_black_box(image):
    """检测图像中的黑框并返回其四个顶点"""
    if image is None or image.size == 0:
        return None

    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 高斯模糊减少噪点
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 使用Canny边缘检测
    edges = cv2.Canny(blurred, config.canny_low, config.canny_high)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 找到最大的矩形轮廓（黑框）
    max_area = 0
    box_points = None

    for contour in contours:
        # 近似多边形，减少轮廓点数
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 只考虑四边形
        if len(approx) != 4:
            continue

        # 计算面积
        area = cv2.contourArea(approx)
        if area > max_area:
            max_area = area
            box_points = approx.reshape(4, 2)

    if box_points is None:
        return None

    # 对四个顶点进行排序：左上、右上、右下、左下
    # 1. 计算中心点
    center = np.mean(box_points, axis=0)

    # 2. 根据点相对于中心的角度排序
    def sort_key(point):
        return math.atan2(point[1] - center[1], point[0] - center[0])

    sorted_points = sorted(box_points, key=sort_key)

    return np.array(sorted_points, dtype=np.float32)


def shrink_quadrilateral(points, shrink_factor=0.1):
    """根据四个顶点向内收缩四边形"""
    if points is None or len(points) != 4:
        return None

    # 计算中心点
    center = np.mean(points, axis=0)

    # 计算每个点向中心移动的方向向量
    shrunk_points = []
    for point in points:
        # 计算点到中心的方向向量
        direction = center - point
        # 按比例收缩
        shrunk_point = point + shrink_factor * direction
        shrunk_points.append(shrunk_point)

    return np.array(shrunk_points, dtype=np.float32)


def extract_center_trajectory(image, quad_points):
    """从四边形区域提取曲线中心轨迹"""
    if quad_points is None or image is None or image.size == 0:
        return None, None, None

    # 1. 计算四边形的最小外接矩形
    x_min, y_min = np.min(quad_points, axis=0)
    x_max, y_max = np.max(quad_points, axis=0)

    # 确保区域在图像范围内
    if x_min < 0 or y_min < 0 or (x_max - x_min) <= 0 or (y_max - y_min) <= 0:
        return None, None, None
    if x_max > image.shape[1] or y_max > image.shape[0]:
        return None, None, None

    # 2. 创建目标矩形（用于透视变换）
    width = int(max(np.linalg.norm(quad_points[0] - quad_points[1]),
                    np.linalg.norm(quad_points[2] - quad_points[3])))
    height = int(max(np.linalg.norm(quad_points[1] - quad_points[2]),
                     np.linalg.norm(quad_points[3] - quad_points[0])))

    # 确保宽度和高度有效
    width = max(10, width)
    height = max(10, height)

    dst_points = np.array([
        [0, 0],
        [width, 0],
        [width, height],
        [0, height]
    ], dtype=np.float32)

    # 3. 计算透视变换矩阵
    M = cv2.getPerspectiveTransform(quad_points, dst_points)

    # 4. 应用透视变换
    warped = cv2.warpPerspective(image, M, (width, height))

    # 5. 在变换后的图像中检测曲线
    # 转换为灰度图并进行处理
    gray = cv2.cvtColor(warped, cv2.COLOR_BGR2GRAY)
    filtered = cv2.medianBlur(gray, config.median_blur_size)

    # 自适应阈值处理
    thresh = cv2.adaptiveThreshold(
        filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV, config.adaptive_block_size, config.adaptive_c
    )

    # 形态学操作
    kernel = np.ones((3, 3), np.uint8)
    opened = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=config.morph_open_iter)
    closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel, iterations=config.morph_close_iter)

    # 骨架提取
    skeleton = cv2.ximgproc.thinning(closed, thinningType=cv2.ximgproc.THINNING_GUOHALL)

    # 找到最大的连通组件
    skeleton_largest = find_largest_connected_component(skeleton)

    # 显示处理步骤
    cv2.imshow('1-Warped', warped)           # 透视变换后的图像
    cv2.imshow('2-Threshold', thresh)        # 二值化图像
    cv2.imshow('3-Morphology', closed)       # 形态学处理后的图像
    cv2.imshow('4-Skeleton', skeleton)       # 原始骨架图
    cv2.imshow('5-Largest Component', skeleton_largest)  # 最大连通组件

    # 从最大连通组件的骨架中提取所有点坐标
    points = np.column_stack(np.where(skeleton_largest > 0))

    # 将坐标转换为(x, y)格式
    if points.size > 0:
        points = points[:, [1, 0]]  # 交换列顺序
    else:
        return None, None, None

    # 使用拓扑排序获取有序轨迹点
    sorted_points = topological_sort_points(points)

    # 6. 将轨迹点映射回原始图像坐标
    if sorted_points is not None and len(sorted_points) > 0:
        # 转换为齐次坐标
        homogenous_points = np.hstack((sorted_points, np.ones((len(sorted_points), 1))))

        # 应用逆变换
        inv_M = np.linalg.inv(M)
        transformed_points = np.dot(homogenous_points, inv_M.T)

        # 归一化
        transformed_points = transformed_points[:, :2] / transformed_points[:, 2][:, np.newaxis]

        # 过滤掉无效点
        valid_points = []
        for point in transformed_points:
            if not np.isnan(point[0]) and not np.isnan(point[1]):
                valid_points.append(point)

        if len(valid_points) == 0:
            return None, None, None

        sorted_points = np.array(valid_points)

    # 创建可视化图像
    result = np.ones_like(warped) * 255
    result[skeleton > 0] = 0

    return sorted_points, quad_points, result


def topological_sort_points(points):
    """使用拓扑排序获取有序轨迹点"""
    # 将点转换为元组集合以便快速查找
    point_set = set(tuple(p) for p in points)

    # 如果没有点，返回空列表
    if not point_set:
        return []

    # 找到端点（只有一个邻居的点）
    endpoints = []
    for p in point_set:
        neighbors = get_8_neighbors(p, point_set)
        if len(neighbors) == 1:
            endpoints.append(p)

    # 如果没有端点（闭环），则使用x坐标最小的点
    if not endpoints:
        start_point = min(point_set, key=lambda p: p[0])
    else:
        # 使用x坐标最小的端点作为起点
        start_point = min(endpoints, key=lambda p: p[0])

    # 使用BFS遍历点
    visited = set()
    queue = deque([start_point])
    sorted_points = []

    while queue:
        current = queue.popleft()
        if current in visited:
            continue

        visited.add(current)
        sorted_points.append(current)

        # 获取当前点的邻居
        neighbors = get_8_neighbors(current, point_set)

        # 按方向连续性排序邻居（优先选择与上一方向一致的邻居）
        if len(sorted_points) > 1:
            prev_point = sorted_points[-2]
            dx = current[0] - prev_point[0]
            dy = current[1] - prev_point[1]

            # 优先选择方向一致的邻居
            neighbors.sort(key=lambda n: abs((n[0] - current[0]) - dx) + abs((n[1] - current[1]) - dy))

        # 添加未访问的邻居
        for neighbor in neighbors:
            if neighbor not in visited:
                queue.append(neighbor)

    # 转换为numpy数组
    return np.array([list(p) for p in sorted_points])


def get_8_neighbors(point, point_set):
    """获取8邻域内的邻居点"""
    x, y = point
    neighbors = []

    # 检查8个方向
    for dx in [-1, 0, 1]:
        for dy in [-1, 0, 1]:
            if dx == 0 and dy == 0:
                continue

            neighbor = (x + dx, y + dy)
            if neighbor in point_set:
                neighbors.append(neighbor)

    return neighbors


def interpolate_trajectory(points, num_points=100):
    """对轨迹点进行样条插值"""
    if points is None or len(points) < 4:
        return points

    # 添加0.1%的随机抖动避免重复x值
    jitter = np.random.normal(0, 0.001, points.shape)
    points = points.astype(float) + jitter

    # 样条插值
    tck, u = splprep(points.T, u=None, s=0.0, per=0)
    u_new = np.linspace(u.min(), u.max(), num_points)
    x_new, y_new = splev(u_new, tck, der=0)

    return np.column_stack((x_new, y_new))


def mouse_callback(event, x, y, flags, param):
    """鼠标回调函数用于手动画框定义ROI区域"""
    if config.manual_roi_mode:
        if event == cv2.EVENT_LBUTTONDOWN:
            # 开始画框
            config.drawing = True
            config.manual_roi_start = (x, y)
            config.manual_roi_end = None

        elif event == cv2.EVENT_MOUSEMOVE and config.drawing:
            # 更新框的结束点
            config.manual_roi_end = (x, y)

        elif event == cv2.EVENT_LBUTTONUP:
            # 完成画框
            if config.drawing and config.manual_roi_start:
                config.drawing = False
                config.manual_roi_end = (x, y)

                # 计算ROI区域
                x1, y1 = config.manual_roi_start
                x2, y2 = config.manual_roi_end

                # 确保坐标顺序正确
                config.roi_x = min(x1, x2)
                config.roi_y = min(y1, y2)
                config.roi_width = abs(x2 - x1)
                config.roi_height = abs(y2 - y1)

                # 检查ROI大小是否合理
                if config.roi_width > 10 and config.roi_height > 10:
                    config.manual_roi_defined = True
                    config.manual_roi_mode = False
                    print(f"✓ 手动ROI定义完成: ({config.roi_x}, {config.roi_y}, {config.roi_width}, {config.roi_height})")

                    # 自动保存ROI设置
                    if save_thresholds():
                        print("✓ ROI设置已自动保存，下次启动将自动加载")

                    print("✓ 轨迹识别已启动")
                else:
                    print("ROI区域太小，请重新选择")


def extract_trajectory_from_manual_roi(image):
    """从手动定义的矩形ROI区域提取轨迹"""
    if not config.manual_roi_defined:
        return None

    # 提取ROI区域
    roi_x, roi_y = config.roi_x, config.roi_y
    roi_w, roi_h = config.roi_width, config.roi_height

    # 检查ROI边界
    if (roi_x < 0 or roi_y < 0 or roi_x + roi_w > image.shape[1] or
        roi_y + roi_h > image.shape[0] or roi_w <= 0 or roi_h <= 0):
        return None

    roi_frame = image[roi_y:roi_y + roi_h, roi_x:roi_x + roi_w]

    if roi_frame.size == 0:
        return None

    # 创建ROI区域的四个角点
    roi_quad = np.array([
        [0, 0],                    # 左上角
        [roi_w, 0],               # 右上角
        [roi_w, roi_h],           # 右下角
        [0, roi_h]                # 左下角
    ], dtype=np.float32)

    # 可选：向内收缩ROI区域以避免边界噪声
    if config.shrink_factor > 0:
        shrunk_quad = shrink_quadrilateral(roi_quad, config.shrink_factor)
    else:
        shrunk_quad = roi_quad

    # 提取轨迹
    trajectory_points, _, _ = extract_center_trajectory(roi_frame, shrunk_quad)

    if trajectory_points is not None and len(trajectory_points) > 0:
        # 转换到原图坐标系
        trajectory_points[:, 0] += roi_x
        trajectory_points[:, 1] += roi_y
        return trajectory_points

    return None

def find_largest_connected_component(binary_image):
    """找到二值图像中最大的连通组件"""
    if binary_image is None or binary_image.size == 0:
        return binary_image

    # 找到所有连通组件
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_image, connectivity=8)

    if num_labels <= 1:  # 只有背景，没有前景
        return binary_image

    # 找到最大的连通组件（排除背景标签0）
    largest_label = 1
    largest_area = stats[1, cv2.CC_STAT_AREA]

    for i in range(2, num_labels):
        area = stats[i, cv2.CC_STAT_AREA]
        if area > largest_area:
            largest_area = area
            largest_label = i

    # 创建只包含最大连通组件的图像
    largest_component = np.zeros_like(binary_image)
    largest_component[labels == largest_label] = 255

    
    #print(f"找到 {num_labels-1} 个连通组件，选择最大的（面积: {largest_area} 像素）")

    return largest_component

def smooth_trajectory(current_trajectory):
    """对轨迹进行时间平滑处理，减少抖动"""
    if current_trajectory is None or len(current_trajectory) == 0:
        return current_trajectory

    # 添加当前轨迹到历史记录
    config.trajectory_history.append(current_trajectory.copy())

    # 保持历史记录大小
    if len(config.trajectory_history) > config.history_size:
        config.trajectory_history.pop(0)

    # 如果历史记录不足，直接返回当前轨迹
    if len(config.trajectory_history) < 2:
        return current_trajectory

    # 计算加权平均
    smoothed_trajectory = current_trajectory.copy()

    # 与历史轨迹进行加权平均
    for i, hist_traj in enumerate(config.trajectory_history[:-1]):
        if len(hist_traj) == len(current_trajectory):
            weight = (i + 1) / len(config.trajectory_history) * (1 - config.smooth_factor)
            smoothed_trajectory = smoothed_trajectory * (1 - weight) + hist_traj * weight

    return smoothed_trajectory

def nothing(x):
    """滑动条回调函数的占位符"""
    pass

def save_thresholds():
    """保存当前阈值参数到文件"""
    try:
        with open(config.threshold_file, 'w') as f:
            f.write(f"{config.canny_low}\n")
            f.write(f"{config.canny_high}\n")
            f.write(f"{config.median_blur_size}\n")
            f.write(f"{config.adaptive_block_size}\n")
            f.write(f"{config.adaptive_c}\n")
            f.write(f"{config.morph_open_iter}\n")
            f.write(f"{config.morph_close_iter}\n")
            f.write(f"{int(config.shrink_factor * 100)}\n")  # 保存为百分比
            f.write(f"{config.roi_x}\n")
            f.write(f"{config.roi_y}\n")
            f.write(f"{config.roi_width}\n")
            f.write(f"{config.roi_height}\n")
            f.write(f"{int(config.manual_roi_defined)}\n")  # 保存ROI是否已定义
        print(f"阈值参数已保存到 {config.threshold_file}")
        return True
    except Exception as e:
        print(f"保存阈值参数失败: {e}")
        return False

def load_thresholds():
    """从文件加载阈值参数"""
    try:
        if os.path.exists(config.threshold_file):
            with open(config.threshold_file, 'r') as f:
                lines = f.readlines()
                if len(lines) >= 12:
                    config.canny_low = int(lines[0].strip())
                    config.canny_high = int(lines[1].strip())
                    config.median_blur_size = int(lines[2].strip())
                    config.adaptive_block_size = int(lines[3].strip())
                    config.adaptive_c = int(lines[4].strip())
                    config.morph_open_iter = int(lines[5].strip())
                    config.morph_close_iter = int(lines[6].strip())
                    config.shrink_factor = int(lines[7].strip()) / 100.0  # 转换为小数
                    config.roi_x = int(lines[8].strip())
                    config.roi_y = int(lines[9].strip())
                    config.roi_width = int(lines[10].strip())
                    config.roi_height = int(lines[11].strip())

                    # 加载ROI定义状态（如果存在第13行）
                    if len(lines) >= 13:
                        config.manual_roi_defined = bool(int(lines[12].strip()))
                        if config.manual_roi_defined:
                            # 自动启用轨迹显示
                            config.show_trajectory = True
                            print(f"✓ 已自动加载ROI区域: ({config.roi_x}, {config.roi_y}, {config.roi_width}, {config.roi_height})")
                            print("✓ 轨迹识别已自动启动")
                            print("✓ 轨迹显示已自动开启")

                    print(f"已从 {config.threshold_file} 加载阈值参数")
                    return True
        else:
            print(f"阈值文件 {config.threshold_file} 不存在，使用默认参数")
            return False
    except Exception as e:
        print(f"加载阈值参数失败: {e}")
        return False


def setup_trackbars():
    """设置参数调整滑动条"""
    cv2.namedWindow('Parameters')
    cv2.resizeWindow('Parameters', 600, 600)

    # 先尝试加载保存的阈值和ROI设置
    if load_thresholds():
        print("=" * 50)
        print("🚀 系统启动状态:")
        if config.manual_roi_defined:
            print("✓ ROI区域已自动加载")
            print("✓ 轨迹识别功能已启动")
            print("✓ 无需手动设置，直接开始识别")
        else:
            print("⚠ 未找到保存的ROI设置")
            print("⚠ 需要按 'r' 键手动设置ROI区域")
        print("=" * 50)
    else:
        print("⚠ 未找到配置文件，使用默认设置")

    # 创建滑动条
    cv2.createTrackbar('Canny Low', 'Parameters', config.canny_low, 200, nothing)
    cv2.createTrackbar('Canny High', 'Parameters', config.canny_high, 300, nothing)
    cv2.createTrackbar('Median Blur', 'Parameters', config.median_blur_size, 50, nothing)
    cv2.createTrackbar('Block Size', 'Parameters', config.adaptive_block_size, 100, nothing)
    cv2.createTrackbar('C Value', 'Parameters', config.adaptive_c, 20, nothing)
    cv2.createTrackbar('Open Iter', 'Parameters', config.morph_open_iter, 5, nothing)
    cv2.createTrackbar('Close Iter', 'Parameters', config.morph_close_iter, 5, nothing)
    cv2.createTrackbar('Shrink Factor', 'Parameters', int(config.shrink_factor * 100), 50, nothing)  # 0-50%收缩

    # 添加平滑参数控制
    cv2.createTrackbar('Smooth Factor', 'Parameters', int(config.smooth_factor * 100), 100, nothing)
    cv2.createTrackbar('History Size', 'Parameters', config.history_size, 10, nothing)

    # 不再使用滑动条作为保存按钮，改用键盘按键

    # ROI位置和大小的滑动条
    cv2.createTrackbar('ROI X', 'Parameters', config.roi_x, 800, nothing)
    cv2.createTrackbar('ROI Y', 'Parameters', config.roi_y, 600, nothing)
    cv2.createTrackbar('ROI Width', 'Parameters', config.roi_width, 800, nothing)
    cv2.createTrackbar('ROI Height', 'Parameters', config.roi_height, 600, nothing)


def update_config_from_trackbars():
    """从滑动条更新配置参数"""
    config.canny_low = cv2.getTrackbarPos('Canny Low', 'Parameters')
    config.canny_high = cv2.getTrackbarPos('Canny High', 'Parameters')

    # 确保中值滤波核大小为奇数
    median_blur = cv2.getTrackbarPos('Median Blur', 'Parameters')
    config.median_blur_size = max(1, median_blur | 1)  # 确保为奇数

    # 确保块大小为奇数且大于1
    block_size = cv2.getTrackbarPos('Block Size', 'Parameters')
    config.adaptive_block_size = max(3, block_size | 1)

    config.adaptive_c = cv2.getTrackbarPos('C Value', 'Parameters')
    config.morph_open_iter = cv2.getTrackbarPos('Open Iter', 'Parameters')
    config.morph_close_iter = cv2.getTrackbarPos('Close Iter', 'Parameters')

    # 更新收缩比例
    shrink_factor = cv2.getTrackbarPos('Shrink Factor', 'Parameters')
    config.shrink_factor = shrink_factor / 100.0  # 转换为0.0-0.5

    # 更新ROI参数
    config.roi_x = cv2.getTrackbarPos('ROI X', 'Parameters')
    config.roi_y = cv2.getTrackbarPos('ROI Y', 'Parameters')
    config.roi_width = max(10, cv2.getTrackbarPos('ROI Width', 'Parameters'))
    config.roi_height = max(10, cv2.getTrackbarPos('ROI Height', 'Parameters'))

    # 更新平滑参数
    config.smooth_factor = cv2.getTrackbarPos('Smooth Factor', 'Parameters') / 100.0
    config.history_size = max(1, cv2.getTrackbarPos('History Size', 'Parameters'))


def save_results(frame, trajectory_points, interpolated_points):
    """保存结果到文件"""
    save_dir = "curve_results"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存原始帧
    frame_path = os.path.join(save_dir, f"frame_{timestamp}.png")
    cv2.imwrite(frame_path, frame)

    # 保存轨迹点数据
    if interpolated_points is not None:
        trajectory_path = os.path.join(save_dir, f"trajectory_points_{timestamp}.npy")
        np.save(trajectory_path, interpolated_points)

    # 创建可视化图像
    if interpolated_points is not None:
        plt.figure(figsize=(12, 8))
        img_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        plt.imshow(img_rgb)

        # 绘制插值后轨迹
        plt.plot(interpolated_points[:, 0], interpolated_points[:, 1],
                 'b-', linewidth=2, label='插值轨迹')

        plt.legend()
        plt.title('曲线轨迹点')

        # 保存可视化结果
        plot_path = os.path.join(save_dir, f"trajectory_plot_{timestamp}.png")
        plt.savefig(plot_path)
        plt.close()

    print(f"结果已保存至: {save_dir}")


def main():
    # 初始化摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("无法打开摄像头")
        return

    # 创建窗口和设置鼠标回调
    cv2.namedWindow('Camera Feed')
    cv2.setMouseCallback('Camera Feed', mouse_callback)

    # 设置参数滑动条
    setup_trackbars()

    print("操作说明:")
    print("🔄 自动功能:")
    print("- 程序启动时自动加载保存的ROI设置")
    print("- 如果ROI已保存，将自动开始轨迹识别")
    print("📋 手动操作:")
    print("1. 按 'r' 键进入手动画框ROI模式（重新设置ROI）")
    print("2. 按 's' 键切换保存帧模式")
    print("3. 按 't' 键切换轨迹显示")
    print("4. 按 'c' 键清除手动ROI")
    print("5. 按 'q' 键退出程序")
    print("6. 按 '+' 键增加收缩比例")
    print("7. 按 '-' 键减少收缩比例")
    print("8. 按 'p' 键保存当前阈值参数到文件")
    print("9. 按 'l' 键从文件加载阈值参数")
    print("🎯 ROI设置:")
    print("- 左键拖拽画矩形框定义ROI区域")
    print("- 松开左键完成ROI定义并自动保存")
    print("- 下次启动时将自动加载并开始识别")
    print("=" * 60)

    while True:
        # 从摄像头读取一帧
        ret, frame = cap.read()
        if not ret:
            print("无法获取帧")
            break

        # 更新配置参数
        update_config_from_trackbars()

        # 处理帧
        processed_frame = frame.copy()

        # 获取ROI区域
        roi_x, roi_y = config.roi_x, config.roi_y
        roi_w, roi_h = config.roi_width, config.roi_height

        # 确保ROI在图像范围内
        roi_x = max(0, min(roi_x, frame.shape[1] - 10))
        roi_y = max(0, min(roi_y, frame.shape[0] - 10))
        roi_w = max(10, min(roi_w, frame.shape[1] - roi_x))
        roi_h = max(10, min(roi_h, frame.shape[0] - roi_y))

        # 绘制ROI区域
        if config.show_roi:
            cv2.rectangle(processed_frame, (roi_x, roi_y),
                          (roi_x + roi_w, roi_y + roi_h),
                          (0, 255, 0), 2)

        # 绘制手动ROI和进行轨迹提取
        interpolated_points = None

        # 绘制正在画的框
        if config.manual_roi_mode and config.manual_roi_start and config.manual_roi_end:
            cv2.rectangle(processed_frame, config.manual_roi_start, config.manual_roi_end, (0, 255, 0), 2)

        # 绘制已完成的手动ROI框
        if config.manual_roi_defined:
            cv2.rectangle(processed_frame,
                         (config.roi_x, config.roi_y),
                         (config.roi_x + config.roi_width, config.roi_y + config.roi_height),
                         (0, 0, 255), 2)

        # 如果已定义手动ROI，进行轨迹提取
        if config.manual_roi_defined:
            trajectory_points = extract_trajectory_from_manual_roi(frame)

            if trajectory_points is not None and len(trajectory_points) > 0:
                # 时间平滑处理，减少抖动
                smoothed_trajectory = smooth_trajectory(trajectory_points)

                # 插值平滑轨迹
                interpolated_points = interpolate_trajectory(smoothed_trajectory, 200)

                    # 绘制轨迹
                if config.show_trajectory and interpolated_points is not None:
                        # 确保坐标点是有效的浮点数
                        for i in range(1, len(interpolated_points)):
                            # 检查坐标点是否有效
                            if (np.isnan(interpolated_points[i - 1]).any() or
                                    np.isnan(interpolated_points[i]).any()):
                                continue

                            # 转换为整数坐标
                            pt1 = (int(interpolated_points[i - 1, 0]), int(interpolated_points[i - 1, 1]))
                            pt2 = (int(interpolated_points[i, 0]), int(interpolated_points[i, 1]))

                            # 绘制线段
                            cv2.line(processed_frame, pt1, pt2, (0, 255, 255), 2)  # 黄色轨迹线

        # 显示状态信息
        if config.manual_roi_mode:
            cv2.putText(processed_frame, "Manual ROI Mode - Drag to draw rectangle",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        elif config.manual_roi_defined:
            cv2.putText(processed_frame, "AUTO MODE - Trajectory extraction active",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            cv2.putText(processed_frame, f"ROI: ({config.roi_x},{config.roi_y},{config.roi_width},{config.roi_height})",
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        else:
            cv2.putText(processed_frame, "Press 'r' to define ROI area",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

        # 显示收缩比例
        shrink_text = f"收缩比例: {int(config.shrink_factor * 100)}%"
        cv2.putText(processed_frame, shrink_text, (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

        # 显示处理后的帧
        cv2.imshow('Camera Feed', processed_frame)

        # 更新参数
        update_config_from_trackbars()

        # 保存帧（如果需要）
        config.frame_counter += 1
        if config.save_frames and config.frame_counter % config.save_interval == 0:
            save_results(frame, trajectory_points, interpolated_points)

        # 处理键盘输入
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('r'):
            config.manual_roi_mode = True
            config.manual_roi_start = None
            config.manual_roi_end = None
            config.drawing = False
            print("进入手动画框ROI模式，左键拖拽画框")
        elif key == ord('c'):
            # 清除手动ROI
            config.manual_roi_mode = False
            config.manual_roi_defined = False
            config.manual_roi_start = None
            config.manual_roi_end = None
            config.drawing = False
            print("手动ROI已清除")
        elif key == ord('s'):
            config.save_frames = not config.save_frames
            print(f"保存帧模式: {'开启' if config.save_frames else '关闭'}")
        elif key == ord('t'):
            config.show_trajectory = not config.show_trajectory
            print(f"轨迹显示: {'开启' if config.show_trajectory else '关闭'}")
        elif key == ord('+'):
            config.shrink_factor = min(0.5, config.shrink_factor + 0.05)
            cv2.setTrackbarPos('Shrink Factor', 'Parameters', int(config.shrink_factor * 100))
            print(f"收缩比例增加至: {int(config.shrink_factor * 100)}%")
        elif key == ord('-'):
            config.shrink_factor = max(0.0, config.shrink_factor - 0.05)
            cv2.setTrackbarPos('Shrink Factor', 'Parameters', int(config.shrink_factor * 100))
            print(f"收缩比例减少至: {int(config.shrink_factor * 100)}%")
        elif key == ord('p'):  # 新增：保存当前阈值参数
            if save_thresholds():
                print("✓ 阈值参数保存成功！")
            else:
                print("✗ 阈值参数保存失败！")
        elif key == ord('l'):  # 新增：手动加载阈值
            if load_thresholds():
                print("✓ 阈值参数加载成功！")
                # 更新滑动条显示
                cv2.setTrackbarPos('Canny Low', 'Parameters', config.canny_low)
                cv2.setTrackbarPos('Canny High', 'Parameters', config.canny_high)
                cv2.setTrackbarPos('Median Blur', 'Parameters', config.median_blur_size)
                cv2.setTrackbarPos('Block Size', 'Parameters', config.adaptive_block_size)
                cv2.setTrackbarPos('C Value', 'Parameters', config.adaptive_c)
                cv2.setTrackbarPos('Open Iter', 'Parameters', config.morph_open_iter)
                cv2.setTrackbarPos('Close Iter', 'Parameters', config.morph_close_iter)
                cv2.setTrackbarPos('Shrink Factor', 'Parameters', int(config.shrink_factor * 100))
                cv2.setTrackbarPos('ROI X', 'Parameters', config.roi_x)
                cv2.setTrackbarPos('ROI Y', 'Parameters', config.roi_y)
                cv2.setTrackbarPos('ROI Width', 'Parameters', config.roi_width)
                cv2.setTrackbarPos('ROI Height', 'Parameters', config.roi_height)
            else:
                print("✗ 阈值参数加载失败！")

    # 释放资源
    cap.release()
    cv2.destroyAllWindows()


if __name__ == "__main__":
    main()