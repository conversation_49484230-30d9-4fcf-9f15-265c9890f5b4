#include "scheduler.h"


// 外部函数声明

// ȫ�ֱ��������ڴ洢��������
uint8_t task_num;
// ȫ��ϵͳʱ�Ӽ����� - 添加保护
static volatile uint32_t systemTimeMs = 0;
static volatile uint32_t systemTimeMs_backup = 0;  // 备份时间
static volatile uint32_t time_error_count = 0;     // 时间错误计数



// ��ʼ��ϵͳʱ��
void SystemTime_Init(void)
{
    // ȷ��SysTick�Ѿ���SysConfig�����ú�
    // ����������Ӷ���ĳ�ʼ������
    systemTimeMs = 0;
}

void SysTick_Handler(void)
{
    systemTimeMs++;
    systemTimeMs_backup = systemTimeMs;  // 同步备份
}
uint32_t GetTick(void)
{
    // 检查时间一致性
    if(systemTimeMs != systemTimeMs_backup) {
        time_error_count++;
        // 使用备份时间恢复
        systemTimeMs = systemTimeMs_backup;
    }

    // 检查异常的时间跳跃
    static uint32_t last_time = 0;
    if(systemTimeMs < last_time && (last_time - systemTimeMs) > 1000) {
        time_error_count++;
        // 时间异常，使用连续时间
        systemTimeMs = last_time + 1;
        systemTimeMs_backup = systemTimeMs;
    }
    last_time = systemTimeMs;

    return systemTimeMs;
}

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;

// ��̬�������飬ÿ�����������������ִ�����ڣ����룩���ϴ�����ʱ�䣨���룩
static task_t scheduler_task[] =
{
   //{Uart_Proc,3,0}
//		{ProcessReceivedData,3,0}
		{Key_Proc,10,0}
	 ,{contorol_Task,1,0}
	 ,{UART1_CheckTimeout,2,0}    // 串口1超时检查，2ms周期（优化：提高检查频率）
	 ,{UART1_ProcessData,3,0}     // 串口1数据处理，3ms周期（优化：提高处理频率）
	 ,{UART2_CheckTimeout,2,0}    // 串口2超时检查，5ms周期
	 ,{UART2_ProcessData,3,0}    // 串口2数据处理，10ms周期
	 ,{dis_read,30,0}
//	 ,{track_read,10,0}
//	 ,{Angle_PID_control,20,0}
		//,{Read_L,1000,0}
  // ,{Angle_PID_control,20,0}   // 循迹控制，100ms周期
//	 ,{motor_stop,1000,0}
};


void scheduler_init(void)
{

    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief ���������к���
 * �����������飬����Ƿ���������Ҫִ�С������ǰʱ���Ѿ����������ִ�����ڣ���ִ�и����񲢸����ϴ�����ʱ��
 */
void scheduler_run(void)
{
  // 遍历任务数组中的所有任务
  for (uint8_t i = 0; i < task_num; i++)
  {
    // 获取当前的系统时间（毫秒）
    uint32_t now_time =GetTick();

    // 检查当前时间是否达到任务的执行时间
    if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
    {
      // 更新任务的上次运行时间为当前时间
      scheduler_task[i].last_run = now_time;

      // 执行任务函数
      scheduler_task[i].task_func();
    }
  }
}

void delay_ms(uint32_t ms) {
    uint32_t start = GetTick();
    while((GetTick() - start) < ms);
}

/**
 * @brief 微秒级延时函数
 * @param us 延时时间（微秒）
 *
 * 基于CPU时钟周期的精确延时，适用于MSPM0G3507 (12MHz)
 * 每个时钟周期约为 83.33ns
 * 1微秒 = 12个时钟周期
 */
void delay_us(uint32_t us) {
    // MSPM0G3507 CPU频率为12MHz
    // 1微秒 = 12个时钟周期
    // 考虑到函数调用和循环开销，实际需要的周期数会稍少

    if (us == 0) return;

    // 每微秒需要的时钟周期数 (12MHz = 12 cycles/us)
    // 减去函数调用和循环的开销约2-3个周期
    uint32_t cycles = us * 12 - 3;

    // 使用内联汇编实现精确延时
    // 每次循环消耗约3个时钟周期 (subs + bne)
    __asm volatile (
        "1: \n\t"
        "subs %0, %0, #3 \n\t"  // 减去3个周期
        "bne 1b \n\t"           // 如果不为0则跳转
        : "+r" (cycles)         // 输入输出：cycles
        :                       // 无额外输入
        : "cc"                  // 影响条件码
    );
}

