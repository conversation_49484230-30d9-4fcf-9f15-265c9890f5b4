#include "usart_mid.h"
#include "Task_func.h"  // 包含电机读取状态管理函数
#include "motor_mid.h"  // 包含电机控制函数

// ==================== 全局变量定义 ====================
// IMU数据处理相关变量
//FrameData_t currentFrame = {0};
uint8_t frameData[19];
uint8_t rectangle_parsed = 0;

FrameData_t currentFrame;

// DMA相关变量
#define UART_PACKET_SIZE  (30)
typedef struct {
    volatile uint8_t buffer[UART_PACKET_SIZE];
    volatile bool dataReady;
} UART_DMA_Handle_t;

UART_DMA_Handle_t gUartDma = {0};
volatile bool gCheckUART = false;

// 串口超时解析相关变量
UART1_Handle_t gUart1Handle = {0};
UART2_Handle_t gUart2Handle = {0};
static uint32_t uart1_data_count = 0;
uint32_t uart_data = 0;

// ==================== 基础串口函数 ====================
void uart_send_char(UART_Regs *uart, char ch)
{
    while(DL_UART_isBusy(uart) == true);
    DL_UART_Main_transmitData(uart, ch);
}

void uart_send_string(UART_Regs *uart, const char* str)
{
    if(str == NULL) return;
    while(*str != '\0') {
        uart_send_char(uart, *str++);
    }
}

void uart_send_data(UART_Regs *uart, const unsigned char* data, int length)
{
    for(int i = 0; i < length; i++) {
        uart_send_char(uart, data[i]);
    }
}

int fputc(int ch, FILE *stream)
{
    while(DL_UART_isBusy(UART_0_INST) == true);
    DL_UART_Main_transmitData(UART_2_INST, ch);
    return ch;
}

// ==================== 辅助转换函数 ====================
int my_strlen(const char* str)
{
    int len = 0;
    if (str) {
        while (str[len] != '\0') len++;
    }
    return len;
}

void int_to_string(int num, char* str)
{
    int i = 0, is_negative = 0;
    if (num == 0) { str[0] = '0'; str[1] = '\0'; return; }
    if (num < 0) { is_negative = 1; num = -num; }

    while (num > 0) { str[i++] = (num % 10) + '0'; num /= 10; }
    if (is_negative) str[i++] = '-';
    str[i] = '\0';

    // 反转字符串
    int start = 0, end = i - 1;
    while (start < end) {
        char temp = str[start]; str[start] = str[end]; str[end] = temp;
        start++; end--;
    }
}

void uint_to_hex_string(unsigned int num, char* str, int uppercase)
{
    int i = 0;
    const char* hex_chars = uppercase ? "0123456789ABCDEF" : "0123456789abcdef";
    if (num == 0) { str[0] = '0'; str[1] = '\0'; return; }

    while (num > 0) { str[i++] = hex_chars[num % 16]; num /= 16; }
    str[i] = '\0';

    // 反转字符串
    int start = 0, end = i - 1;
    while (start < end) {
        char temp = str[start]; str[start] = str[end]; str[end] = temp;
        start++; end--;
    }
}

void float_to_string(float num, char* str, int precision)
{
    int int_part = (int)num;
    float frac_part = num - int_part;

    if (num < 0) { *str++ = '-'; int_part = -int_part; frac_part = -frac_part; }

    char int_str[12];
    int_to_string(int_part, int_str);
    int i = 0;
    while (int_str[i] != '\0') *str++ = int_str[i++];

    *str++ = '.';
    for (int j = 0; j < precision; j++) {
        frac_part *= 10;
        int digit = (int)frac_part;
        *str++ = digit + '0';
        frac_part -= digit;
    }
    *str = '\0';
}

// ==================== 格式化输出函数 ====================
int uart_printf(UART_Regs *uart, const char *format, ...)
{
    if (!uart || !format) return 0;

    va_list args;
    va_start(args, format);
    const char *p = format;
    int count = 0;
    char buffer[32];

    while (*p) {
        if (*p == '%' && *(p + 1)) {
            p++; // 跳过 '%'

            // 解析精度（对于浮点数）
            int precision = 1; // 默认精度1位
            if (*p == '.') {
                p++;
                precision = 0;
                while (*p >= '0' && *p <= '9') {
                    precision = precision * 10 + (*p - '0');
                    p++;
                }
            }

            switch (*p) {
                case 'd': {
                    int val = va_arg(args, int);
                    int_to_string(val, buffer);
                    uart_send_string(uart, buffer);
                    count += my_strlen(buffer);
                    break;
                }
                case 'x': case 'X': {
                    unsigned int val = va_arg(args, unsigned int);
                    uint_to_hex_string(val, buffer, (*p == 'X'));
                    uart_send_string(uart, buffer);
                    count += my_strlen(buffer);
                    break;
                }
                case 'c': {
                    char ch = (char)va_arg(args, int);
                    uart_send_char(uart, ch);
                    count++;
                    break;
                }
                case 's': {
                    char *str = va_arg(args, char*);
                    if (str) {
                        uart_send_string(uart, str);
                        count += my_strlen(str);
                    }
                    break;
                }
                case 'f': {
                    double val = va_arg(args, double);
                    float_to_string((float)val, buffer, precision);
                    uart_send_string(uart, buffer);
                    count += my_strlen(buffer);
                    break;
                }
                case '%': {
                    uart_send_char(uart, '%');
                    count++;
                    break;
                }
                default:
                    uart_send_char(uart, *p);
                    count++;
                    break;
            }
        } else {
            uart_send_char(uart, *p);
            count++;
        }
        p++;
    }

    va_end(args);
    return count;
}

// ==================== 调试辅助函数 ====================
void send_hex_byte(uint8_t byte)
{
    char hex_chars[] = "0123456789ABCDEF";
    uart_send_char(UART_2_INST, hex_chars[(byte >> 4) & 0x0F]);
    uart_send_char(UART_2_INST, hex_chars[byte & 0x0F]);
}


// ==================== 初始化函数 ====================
void UART_DMA_Init(void)
{
    // 串口0 DMA配置
    DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)(&UART_0_INST->RXDATA));
    DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gUartDma.buffer[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, UART_PACKET_SIZE);
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
    gUartDma.dataReady = false;

   
    // 初始化超时解析
    UART1_Timeout_Init();
    UART2_Timeout_Init();
}

void DMA_REST(void)
{
    DL_DMA_disableChannel(DMA, DMA_CH0_CHAN_ID);
    DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gUartDma.buffer[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, UART_PACKET_SIZE);
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);
    gUartDma.dataReady = false;
}




// ==================== CRC和数据处理函数 ====================







// 官方CRC16查找表
const uint16_t CRC16_table[256] =
{
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50A5, 0x60C6, 0x70E7, 0x8108, 0x9129,
    0xA14A, 0xB16B, 0xC18C, 0xD1AD, 0xE1CE, 0xF1EF,
    0x1231, 0x0210, 0x3273, 0x2252, 0x52B5, 0x4294, 0x72F7, 0x62D6, 0x9339, 0x8318,
    0xB37B, 0xA35A, 0xD3BD, 0xC39C, 0xF3FF, 0xE3DE,
    0x2462, 0x3443, 0x0420, 0x1401, 0x64E6, 0x74C7, 0x44A4, 0x5485, 0xA56A, 0xB54B,
    0x8528, 0x9509, 0xE5EE, 0xF5CF, 0xC5AC, 0xD58D,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76D7, 0x66F6, 0x5695, 0x46B4, 0xB75B, 0xA77A,
    0x9719, 0x8738, 0xF7DF, 0xE7FE, 0xD79D, 0xC7BC,
    0x48C4, 0x58E5, 0x6886, 0x78A7, 0x0840, 0x1861, 0x2802, 0x3823, 0xC9CC, 0xD9ED,
    0xE98E, 0xF9AF, 0x8948, 0x9969, 0xA90A, 0xB92B,
    0x5AF5, 0x4AD4, 0x7AB7, 0x6A96, 0x1A71, 0x0A50, 0x3A33, 0x2A12, 0xDBFD, 0xCBDC,
    0xFBBF, 0xEB9E, 0x9B79, 0x8B58, 0xBB3B, 0xAB1A,
    0x6CA6, 0x7C87, 0x4CE4, 0x5CC5, 0x2C22, 0x3C03, 0x0C60, 0x1C41, 0xEDAE, 0xFD8F,
    0xCDEC, 0xDDCD, 0xAD2A, 0xBD0B, 0x8D68, 0x9D49,
    0x7E97, 0x6EB6, 0x5ED5, 0x4EF4, 0x3E13, 0x2E32, 0x1E51, 0x0E70, 0xFF9F, 0xEFBE,
    0xDFDD, 0xCFFC, 0xBF1B, 0xAF3A, 0x9F59, 0x8F78,
    0x9188, 0x81A9, 0xB1CA, 0xA1EB, 0xD10C, 0xC12D, 0xF14E, 0xE16F, 0x1080, 0x00A1,
    0x30C2, 0x20E3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83B9, 0x9398, 0xA3FB, 0xB3DA, 0xC33D, 0xD31C, 0xE37F, 0xF35E, 0x02B1, 0x1290,
    0x22F3, 0x32D2, 0x4235, 0x5214, 0x6277, 0x7256,
    0xB5EA, 0xA5CB, 0x95A8, 0x8589, 0xF56E, 0xE54F, 0xD52C, 0xC50D, 0x34E2, 0x24C3,
    0x14A0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
    0xA7DB, 0xB7FA, 0x8799, 0x97B8, 0xE75F, 0xF77E, 0xC71D, 0xD73C, 0x26D3, 0x36F2,
    0x0691, 0x16B0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xD94C, 0xC96D, 0xF90E, 0xE92F, 0x99C8, 0x89E9, 0xB98A, 0xA9AB, 0x5844, 0x4865,
    0x7806, 0x6827, 0x18C0, 0x08E1, 0x3882, 0x28A3,
    0xCB7D, 0xDB5C, 0xEB3F, 0xFB1E, 0x8BF9, 0x9BD8, 0xABBB, 0xBB9A, 0x4A75, 0x5A54,
    0x6A37, 0x7A16, 0x0AF1, 0x1AD0, 0x2AB3, 0x3A92,
    0xFD2E, 0xED0F, 0xDD6C, 0xCD4D, 0xBDAA, 0xAD8B, 0x9DE8, 0x8DC9, 0x7C26, 0x6C07,
    0x5C64, 0x4C45, 0x3CA2, 0x2C83, 0x1CE0, 0x0CC1,
    0xEF1F, 0xFF3E, 0xCF5D, 0xDF7C, 0xAF9B, 0xBFBA, 0x8FD9, 0x9FF8, 0x6E17, 0x7E36,
    0x4E55, 0x5E74, 0x2E93, 0x3EB2, 0x0ED1, 0x1EF0
};

// 官方CRC16计算函数
uint16_t Get_CRC16(uint8_t *ptr, uint16_t len)
{
    uint16_t crc = 0xFFFF;
    for (uint16_t i = 0; i < len; ++i)
    {
        uint8_t index = (crc >> 8) ^ ptr[i];  // 修正：添加括号
        crc = ((crc << 8) ^ CRC16_table[index]);
    }
    return crc;
}



// 官方数据转换函数
float uint_to_float(int x_int, float x_min, float x_max, int bits)
{
    /* converts unsigned int to float, given range and number of bits */
    float span = x_max - x_min;
    float offset = x_min;
    return ((float)x_int) * span / ((float)((1 << bits) - 1)) + offset;
}




int parse_imu_frame(uint8_t *data, FrameData_t *out)
{
    // 检查帧头和帧尾
    if (data[0] != 0x55 || data[1] != 0xAA || data[18] != 0x0A)
        return -1;

    // 解析数据
    out->slave_id = data[2];

    // Roll角：DATA[4-7] (32位浮点数，小端序)
    uint32_t roll_uint = (data[7]<<24) | (data[6]<<16) | (data[5]<<8) | data[4];
    float *roll_ptr = (float*)&roll_uint;
    out->roll = *roll_ptr;

    // Pitch角：DATA[8-11] (32位浮点数，小端序)
    uint32_t pitch_uint = (data[11]<<24) | (data[10]<<16) | (data[9]<<8) | data[8];
    float *pitch_ptr = (float*)&pitch_uint;
    out->pitch = *pitch_ptr;

    // Yaw角：DATA[12-15] (32位浮点数，小端序)
    uint32_t yaw_uint = (data[15]<<24) | (data[14]<<16) | (data[13]<<8) | data[12];
    float *yaw_ptr = (float*)&yaw_uint;
    out->yaw = *yaw_ptr;

    return 0;
}



int find_frame(uint8_t *buffer, int length, uint8_t *frame)
{
    for (int i = 0; i <= length - 19; i++) {
        if (buffer[i] == 0x55 && buffer[i+1] == 0xAA && buffer[i+18] == 0x0A) {
            memcpy(frame, &buffer[i], 19);
            return i;
        }
    }
    return -1;
}



uint8_t angle_flag=0;

void ProcessReceivedData(void)
{
    if(gCheckUART) {
   
       

        // 重置DMA准备下次接收
        DL_DMA_disableChannel(DMA, DMA_CH0_CHAN_ID);
        DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gUartDma.buffer[0]);
        DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, UART_PACKET_SIZE);
        DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);
        gCheckUART = false;
    }
}

// 串口1超时解析功能实现
void UART1_Timeout_Init(void)
{
    // 初始化串口1超时解析结构体
    gUart1Handle.rxIndex = 0;
    gUart1Handle.dataReady = false;
    gUart1Handle.lastRxTime = 0;
    gUart1Handle.timeoutFlag = false;

		    // 串口1中断配置
    DL_UART_enableInterrupt(UART_1_INST, DL_UART_INTERRUPT_RX);
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);

    // 清空接收缓冲区
    for(uint16_t i = 0; i < UART1_PACKET_SIZE; i++) {
        gUart1Handle.buffer[i] = 0;
    }


}

void UART1_CheckTimeout(void)
{
    uint32_t currentTime = GetTick();

    // 检查是否有数据且超时
		
    if(gUart1Handle.rxIndex > 0 && !gUart1Handle.timeoutFlag && !gUart1Handle.dataReady) 
		{
			if(rectangle_parsed||gUart1Handle.rxIndex > 25) // 1 完毕了 0 没完毕
			{
       
				if((currentTime - gUart1Handle.lastRxTime) >= UART1_TIMEOUT_MS) 
				{
            gUart1Handle.timeoutFlag = true;
            gUart1Handle.dataReady = true;
        }
				
			}
    }
}

/**
 * @brief UART1数据处理函数 - 电机速度读取
 *
 * 功能：
 * 1. 解析电机速度读取命令的响应数据
 * 2. 支持正确返回和错误返回两种格式
 * 3. 正确返回：地址 + 0x35 + 符号 + 转速数据 + 校验字节 (6字节)
 * 4. 错误返回：地址 + 0x00 + 校验字节 (3字节)
 *
 * 数据格式：
 * - 符号位：01表示负数，00表示正数
 * - 转速数据：16位无符号整数，单位RPM
 *
 * 示例：
 * - 正确返回：01 35 01 05 DC 6B -> -1500 RPM
 * - 错误返回：01 00 EE 6B -> 错误响应
 */
double distance=0;

double angleToDistance(double angle, double radius) {
    const double PI = 3.14159265358979323846; // 定义π的值
    double distance = (angle / 360.0) * 2 * PI * radius; // 计算距离
    return distance;
}

int16_t Motor_Speed_RPM[2] = {0,0};
float Motor_Cur_Pos[2] ={0,0};
extern void motor_packet_sliding_parse(const uint8_t* uart_buffer, uint16_t buffer_len);
void UART1_ProcessData(void)
{
    if(gUart1Handle.dataReady) {
        uart1_data_count++; // 数据接收计次器递增
        
        // 使用滑动缓冲区解析电机数据
        motor_packet_sliding_parse((uint8_t*)gUart1Handle.buffer, gUart1Handle.rxIndex);
        
        // 重置状态
        gUart1Handle.rxIndex = 0;
        gUart1Handle.dataReady = false;
        gUart1Handle.timeoutFlag = false;

        // 清空缓冲区
        for(uint16_t i = 0; i < UART1_PACKET_SIZE; i++) {
            gUart1Handle.buffer[i] = 0;
        }
    }
}

uint8_t speed_cal_read(void)
{
	if(Motor_Speed_RPM[0]==0&&Motor_Speed_RPM[1]==0)
	{
		return 1;
	}else
	{
		return 0;
	}
}



// 串口2超时解析功能实现
void UART2_Timeout_Init(void)
{
    // 初始化串口2超时解析结构体
    gUart2Handle.rxIndex = 0;
    gUart2Handle.dataReady = false;
    gUart2Handle.lastRxTime = 0;
    gUart2Handle.timeoutFlag = false;

    // 清空接收缓冲区
    for(uint16_t i = 0; i < UART2_PACKET_SIZE; i++) {
        gUart2Handle.buffer[i] = 0;
    }

    // 使能串口2接收中断
    DL_UART_enableInterrupt(UART_2_INST, DL_UART_INTERRUPT_RX);
    NVIC_EnableIRQ(UART_2_INST_INT_IRQN);
}

void UART2_CheckTimeout(void)
{
    uint32_t currentTime = GetTick();

    // 检查是否有数据且超时
    if(gUart2Handle.rxIndex > 0 && !gUart2Handle.timeoutFlag && !gUart2Handle.dataReady) {
        if((currentTime - gUart2Handle.lastRxTime) >= UART2_TIMEOUT_MS) {
            gUart2Handle.timeoutFlag = true;
            gUart2Handle.dataReady = true;
        }
    }
}




extern void new_vision_packet_parse_example(const uint8_t* uart_buffer, uint16_t buffer_len);
extern void new_vision_packet_parse_examplaa(const uint8_t* uart_buffer, uint16_t buffer_len);
extern void vision_packet_sliding_parse(const uint8_t* uart_buffer, uint16_t buffer_len);


void UART2_ProcessData(void)
{
    if(gUart2Handle.dataReady) {
        // 使用新的解析函数处理串口2数据
        vision_packet_sliding_parse((uint8_t*)gUart2Handle.buffer, gUart2Handle.rxIndex);
        
        // 重置状态
        gUart2Handle.rxIndex = 0;
        gUart2Handle.dataReady = false;
        gUart2Handle.timeoutFlag = false;

        // 清空缓冲区
        for(uint16_t i = 0; i < UART2_PACKET_SIZE; i++) {
            gUart2Handle.buffer[i] = 0;
        }
    }
}


// ==================== 串口中断处理函数 ====================
// 串口0中断处理函数 - DMA接收完成
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST)) {
        case DL_UART_MAIN_IIDX_DMA_DONE_RX:
            gCheckUART = true;
            break;
        default:
            break;
    }
}

// 串口1中断处理函数 - 超时解析
void UART_1_INST_IRQHandler(void)
{
    switch(DL_UART_getPendingInterrupt(UART_1_INST)) 
			{
        case DL_UART_IIDX_RX:
            uart_data = DL_UART_Main_receiveData(UART_1_INST);
            // 检查并修复损坏的索引
            if(gUart1Handle.rxIndex > 60) {
                gUart1Handle.rxIndex = 0;
                gUart1Handle.dataReady = false;
                gUart1Handle.timeoutFlag = false;
            }
            // 回显已禁用，避免过多输出
						//uart_send_char(UART_2_INST, uart_data);  
            // 接收数据到UART1缓冲区
            if(gUart1Handle.rxIndex < 60) {
                gUart1Handle.buffer[gUart1Handle.rxIndex] = uart_data;
                gUart1Handle.rxIndex++;

                gUart1Handle.lastRxTime = GetTick();
                gUart1Handle.timeoutFlag = false;
            }
            break;
        default:
            break;
    }
}

// 串口2中断处理函数 - 命令解析
void UART_2_INST_IRQHandler(void)
{
    switch(DL_UART_getPendingInterrupt(UART_2_INST)) 
		{
        case DL_UART_IIDX_RX:
            uart_data = DL_UART_Main_receiveData(UART_2_INST);

            // 检查并修复损坏的索引
            if(gUart2Handle.rxIndex > 60)
							{
                gUart2Handle.rxIndex = 0;
                gUart2Handle.dataReady = false;
                gUart2Handle.timeoutFlag = false;
            }

            // 接收数据
            if(gUart2Handle.rxIndex < 60) {
                gUart2Handle.buffer[gUart2Handle.rxIndex] = uart_data;
                gUart2Handle.rxIndex++;
                gUart2Handle.lastRxTime = GetTick();
                gUart2Handle.timeoutFlag = false;


                
            }
            break;
        default:
            break;
    }
}




void vision_packet_sliding_parse(const uint8_t* uart_buffer, uint16_t buffer_len) 
{
    static uint8_t rxBuffer[128];  // 滑动缓冲区
    static uint16_t rxCounter = 0; // 缓冲区数据计数
    
    // 将新数据添加到滑动缓冲区
    for(uint16_t i = 0; i < buffer_len && rxCounter < 128; i++) {
        rxBuffer[rxCounter++] = uart_buffer[i];
    }
    
    // 处理缓冲区中的所有完整数据包
    uint16_t processIndex = 0;
    while(processIndex + 4 < rxCounter) {
        // 查找帧头 0xAA 0x55
        if(rxBuffer[processIndex] == 0xAA && rxBuffer[processIndex + 1] == 0x55) {
            uint8_t cmd = rxBuffer[processIndex + 2];
            uint8_t dataLen = rxBuffer[processIndex + 3];
            uint16_t totalLen = 5 + dataLen; // 帧头(2) + 命令(1) + 长度(1) + 数据(N) + 校验(1)
            
            // 检查数据长度是否合理
            if(dataLen > 32) {
                processIndex++;
                continue;
            }
            
            // 检查是否有完整的数据包
            if(processIndex + totalLen > rxCounter) {
                break; // 数据包不完整，等待更多数据
            }
            
            // 计算校验和
            uint8_t checksum = 0;
            for(int i = processIndex + 2; i < processIndex + totalLen - 1; i++) {
                checksum += rxBuffer[i];
            }
            
            // 验证校验和
            if(checksum == rxBuffer[processIndex + totalLen - 1]) {
                const uint8_t* data = &rxBuffer[processIndex + 4];
                
                // 优先处理方框数据
                if(cmd == 0x01 && dataLen >= 16) {
                    // 矩形数据处理 - 小端序解析
                    uint16_t x1 = data[0] + (data[1] << 8);
                    uint16_t y1 = data[2] + (data[3] << 8);
                    uint16_t x2 = data[4] + (data[5] << 8);
                    uint16_t y2 = data[6] + (data[7] << 8);
                    uint16_t x3 = data[8] + (data[9] << 8);
                    uint16_t y3 = data[10] + (data[11] << 8);
                    uint16_t x4 = data[12] + (data[13] << 8);
                    uint16_t y4 = data[14] + (data[15] << 8);
                    get_data(x1, y1, x2, y2, x3, y3, x4, y4);
                    rectangle_parsed = 1; // 标记方框已解析
                }
                // 只有方框解析完成后才处理激光点
                else if(cmd == 0x02 && dataLen >= 4 && rectangle_parsed == 1) {
                    // 红色目标数据处理 - 小端序解析
                    uint16_t x = data[0] + (data[1] << 8);
                    uint16_t y = data[2] + (data[3] << 8);

                    read_place(x, y);
                }
                // 新增：处理两个点坐标数据 (0x03指令)
                else if(cmd == 0x03 && dataLen >= 8) {
                    // 解析两个点的坐标 - 小端序解析
                    uint16_t point1_x = data[0] + (data[1] << 8);  // 第一个点X坐标
                    uint16_t point1_y = data[2] + (data[3] << 8);  // 第一个点Y坐标
                    uint16_t point2_x = data[4] + (data[5] << 8);  // 第二个点X坐标
                    uint16_t point2_y = data[6] + (data[7] << 8);  // 第二个点Y坐标

                    // 调试输出
                    uart_printf(UART_0_INST, "收到两点坐标: 点1(%d,%d) 点2(%d,%d)\r\n",
                               point1_x, point1_y, point2_x, point2_y);

                    // TODO: 在这里添加您的处理函数
                    // 例如：process_two_points(point1_x, point1_y, point2_x, point2_y);

                    // 预留位置供您添加自定义处理函数
                    handle_two_points_data(point1_x, point1_y, point2_x, point2_y);
                }
                
                processIndex += totalLen; // 跳过已处理的数据包
            } else {
                processIndex++; // 校验失败，继续查找
            }
        } else {
            processIndex++; // 不是帧头，继续查找
        }
    }
    
    // 移除已处理的数据，保留未处理的数据
    if(processIndex > 0) {
        for(int i = 0; i < rxCounter - processIndex; i++) {
            rxBuffer[i] = rxBuffer[i + processIndex];
        }
        rxCounter -= processIndex;
    }
    
    // 防止缓冲区溢出
    if(rxCounter > 100) {

        rxCounter = 0; // 重置缓冲区
    }
}

void motor_packet_sliding_parse(const uint8_t* uart_buffer, uint16_t buffer_len) 
{
    static uint8_t rxBuffer[128];  // 滑动缓冲区
    static uint16_t rxCounter = 0; // 缓冲区数据计数
     
    // 将新数据添加到滑动缓冲区
    for(uint16_t i = 0; i < buffer_len && rxCounter < 128; i++) {
        rxBuffer[rxCounter++] = uart_buffer[i];
    }
    
    // 处理缓冲区中的所有完整数据包
    uint16_t processIndex = 0;
    while(processIndex + 8 <= rxCounter) {
        // 检查电机位置读取数据包格式：ID + 0x36 + 4字节位置数据 + 2字节校验 = 8字节
        if(rxBuffer[processIndex + 1] == 0x36 && (processIndex + 8) <= rxCounter) {
            uint8_t motor_id = rxBuffer[processIndex];
            uint32_t pos = 0;
            
            // 验证电机ID有效性 (1或2)
            if(motor_id == 1 || motor_id == 2) {
                // 拼接成uint32_t类型 (小端序)
                pos = (uint32_t)(
                    ((uint32_t)rxBuffer[processIndex + 3] << 24) |
                    ((uint32_t)rxBuffer[processIndex + 4] << 16) |
                    ((uint32_t)rxBuffer[processIndex + 5] << 8)  |
                    ((uint32_t)rxBuffer[processIndex + 6] << 0)
                );

                // 转换成角度
                Motor_Cur_Pos[motor_id - 1] = (float)pos * 360.0f / 65536.0f;

//                uart_printf(UART_0_INST, "Motor[%d]: %.2f deg\r\n", 
//                           motor_id, Motor_Cur_Pos[motor_id - 1]);
//                
                processIndex += 8; // 跳过已处理的8字节数据包
            } else {
                processIndex++; // ID无效，继续查找
            }
        } else {
            processIndex++; // 不是有效数据包，继续查找
        }
    }
    
    // 移除已处理的数据，保留未处理的数据
    if(processIndex > 0) {
        for(int i = 0; i < rxCounter - processIndex; i++) {
            rxBuffer[i] = rxBuffer[i + processIndex];
        }
        rxCounter -= processIndex;
    }
    
    // 防止缓冲区溢出
    if(rxCounter > 100) {
        rxCounter = 0; // 重置缓冲区
    }
}
