#ifndef _MID_MOTOR_H_
#define _MID_MOTOR_H_
#include "mydefine.h"
void motor_init(void);
void motor_distance(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint32_t pulse,uint8_t mode,uint8_t sync);
void motor_speed(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint8_t sync);
void motor_readdistance(uint8_t id);
void motor_readspeed(uint8_t id);
void motor_sync(void);
void motor_stop(void);
void motor_clear_position(uint8_t id);
void motor_clear_all_position(void);
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent);
void dis_read(void);
void motor_curve(uint8_t addr, uint8_t dir, uint16_t acc, uint16_t dec, float velocity, float position, uint8_t raf, uint8_t snF);


// 电机角度移动函数
void motor_move_angle(float angle_x, float angle_y, uint8_t speed_percent, uint8_t accel);

// 运动学解算函数
void get_current_target_coordinate(float* current_x, float* current_y);                    // 获取当前指向坐标
void calculate_target_angles(float target_x, float target_y, float* target_pan, float* target_tilt); // 坐标转角度
void move_to_coordinate(float target_x, float target_y, uint8_t speed);                    // 移动到目标坐标
void move_to_angle(float target_pan, float target_tilt, uint8_t speed);                    // 移动到目标角度

// 位置检查函数
uint8_t check_motor_position_in_range(float target_x, float target_y, float tolerance_x, float tolerance_y);
uint8_t check_coordinate_reached(float target_x, float target_y, float tolerance_x, float tolerance_y); // 检查坐标是否到达

// 测试函数
void motor_simple_test(void);  // 简单电机测试
void gimbal_move_relative(float delta_pan, float delta_tilt, uint8_t speed); // 相对角度移动
void gimbal_test_conversion(void);                                     // 测试角度像素转换

#endif
