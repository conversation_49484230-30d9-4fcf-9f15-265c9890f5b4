#ifndef _MID_MOTOR_H_
#define _MID_MOTOR_H_
#include "mydefine.h"
void motor_init(void);
void motor_distance(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint32_t pulse,uint8_t mode,uint8_t sync);
void motor_speed(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint8_t sync);
void motor_readdistance(uint8_t id);
void motor_readspeed(uint8_t id);
void motor_sync(void);
void motor_stop(void);
void motor_clear_position(uint8_t id);
void motor_clear_all_position(void);
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent);
void dis_read(void);
void motor_curve(uint8_t addr, uint8_t dir, uint16_t acc, uint16_t dec, float velocity, float position, uint8_t raf, uint8_t snF);
void motor_move_angle(float angle_x, float angle_y, uint8_t speed_percent, uint8_t accel);
void motor_angle_example(void);

// 云台角度像素转换配置结构体
typedef struct {
    float fov_horizontal_deg;    // 水平视场角
    float fov_vertical_deg;      // 垂直视场角
    int image_width;             // 图像宽度
    int image_height;            // 图像高度
    int image_center_x;          // 图像中心X
    int image_center_y;          // 图像中心Y
    float max_pan_deg;           // 最大水平角度
    float max_tilt_deg;          // 最大俯仰角度
} gimbal_config_t;
uint8_t check_motor_position_in_range(float target_x, float target_y, float tolerance_x, float tolerance_y);
// 基础角度像素转换函数
int angle_to_pixel_x(float angle);                                    // 水平角度转像素X坐标
int angle_to_pixel_y(float angle);                                    // 垂直角度转像素Y坐标
float pixel_to_angle_x(int pixel_x);                                  // 像素X坐标转水平角度
float pixel_to_angle_y(int pixel_y);                                  // 像素Y坐标转垂直角度
void move_to_pixel(int pixel_x, int pixel_y, uint8_t speed);          // 移动到指定像素位置

// 云台配置和控制函数
void gimbal_set_config(float fov_h, float fov_v, int img_w, int img_h, float max_pan, float max_tilt); // 设置云台配置
gimbal_config_t* gimbal_get_config(void);                             // 获取当前云台配置
void gimbal_set_center(int center_x, int center_y);                   // 设置图像中心点（标定用）
void gimbal_move_to_angle(float pan_deg, float tilt_deg, uint8_t speed); // 移动到绝对角度位置
void gimbal_move_relative(float delta_pan, float delta_tilt, uint8_t speed); // 相对角度移动
void gimbal_test_conversion(void);                                     // 测试角度像素转换

#endif
