#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轨迹简化和坐标转换模块
用于云台激光循迹系统
"""

import numpy as np
import math
import serial
import json
import time

class TrajectoryProcessor:
    def __init__(self):
        # 云台参数配置
        self.kinematic_distance = 0.9  # 目标距离(米)
        self.image_width = 640
        self.image_height = 480
        self.image_center_x = 320
        self.image_center_y = 240
        self.fov_horizontal = 17.5  # 水平视场角(度)
        self.fov_vertical = 11.2    # 垂直视场角(度)
        
        # 轨迹简化参数
        self.simplify_tolerance = 2.0  # 道格拉斯-普克算法容差(像素)
        self.min_points = 10          # 最少保留点数
        self.max_points = 50          # 最多保留点数
        
        # 串口通信
        self.serial_port = None
        self.serial_baudrate = 115200
        
    def douglas_peucker(self, points, tolerance):
        """道格拉斯-普克算法简化轨迹"""
        if len(points) <= 2:
            return points
            
        # 找到距离首尾连线最远的点
        start = points[0]
        end = points[-1]
        max_distance = 0
        max_index = 0
        
        for i in range(1, len(points) - 1):
            distance = self.point_to_line_distance(points[i], start, end)
            if distance > max_distance:
                max_distance = distance
                max_index = i
        
        # 如果最大距离小于容差，简化为首尾两点
        if max_distance < tolerance:
            return [start, end]
        
        # 递归处理两段
        left_points = self.douglas_peucker(points[:max_index + 1], tolerance)
        right_points = self.douglas_peucker(points[max_index:], tolerance)
        
        # 合并结果（去除重复点）
        return left_points[:-1] + right_points
    
    def point_to_line_distance(self, point, line_start, line_end):
        """计算点到直线的距离"""
        x0, y0 = point
        x1, y1 = line_start
        x2, y2 = line_end
        
        # 直线长度
        line_length = math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
        if line_length == 0:
            return math.sqrt((x0 - x1)**2 + (y0 - y1)**2)
        
        # 点到直线距离公式
        distance = abs((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1) / line_length
        return distance
    
    def adaptive_simplify(self, points):
        """自适应轨迹简化"""
        if len(points) <= self.min_points:
            return points
        
        # 从小容差开始，逐步增加直到点数合适
        tolerance = 1.0
        simplified = points
        
        while len(simplified) > self.max_points and tolerance < 20.0:
            simplified = self.douglas_peucker(points, tolerance)
            tolerance += 0.5
        
        # 确保至少保留最少点数
        if len(simplified) < self.min_points:
            # 等间距采样
            indices = np.linspace(0, len(points) - 1, self.min_points, dtype=int)
            simplified = [points[i] for i in indices]
        
        return simplified
    
    def pixel_to_angle(self, pixel_x, pixel_y):
        """像素坐标转云台角度"""
        # 归一化到[-1, 1]
        norm_x = (pixel_x - self.image_center_x) / (self.image_width / 2)
        norm_y = (pixel_y - self.image_center_y) / (self.image_height / 2)
        
        # 转换为角度
        pan_angle = norm_x * (self.fov_horizontal / 2)    # 水平角
        tilt_angle = -norm_y * (self.fov_vertical / 2)    # 俯仰角(Y轴反向)
        
        return pan_angle, tilt_angle
    
    def pixel_to_coordinate(self, pixel_x, pixel_y):
        """像素坐标转空间坐标"""
        pan_angle, tilt_angle = self.pixel_to_angle(pixel_x, pixel_y)
        
        # 角度转弧度
        pan_rad = math.radians(pan_angle)
        tilt_rad = math.radians(tilt_angle)
        
        # 运动学正解
        target_y = self.kinematic_distance * math.tan(pan_rad)   # 水平坐标
        target_x = self.kinematic_distance * math.tan(tilt_rad)  # 高度坐标
        
        return target_x, target_y
    
    def process_trajectory(self, interpolated_points):
        """处理轨迹：简化 + 坐标转换"""
        if len(interpolated_points) == 0:
            return []
        
        # 1. 轨迹简化
        simplified_pixels = self.adaptive_simplify(interpolated_points.tolist())
        
        # 2. 坐标转换
        trajectory_coords = []
        trajectory_angles = []
        
        for pixel_point in simplified_pixels:
            # 像素转空间坐标
            coord_x, coord_y = self.pixel_to_coordinate(pixel_point[0], pixel_point[1])
            trajectory_coords.append([coord_x, coord_y])
            
            # 像素转角度
            angle_pan, angle_tilt = self.pixel_to_angle(pixel_point[0], pixel_point[1])
            trajectory_angles.append([angle_pan, angle_tilt])
        
        return {
            'pixel_points': simplified_pixels,
            'coordinates': trajectory_coords,
            'angles': trajectory_angles,
            'count': len(simplified_pixels)
        }
    
    def init_serial(self, port='COM3'):
        """初始化串口通信"""
        try:
            self.serial_port = serial.Serial(port, self.serial_baudrate, timeout=1)
            print(f"串口 {port} 连接成功")
            return True
        except Exception as e:
            print(f"串口连接失败: {e}")
            return False
    
    def send_trajectory_to_gimbal(self, trajectory_data):
        """发送轨迹数据到云台"""
        if not self.serial_port:
            print("串口未连接")
            return False
        
        try:
            # 构造数据包
            data_packet = {
                'cmd': 'trajectory',
                'count': trajectory_data['count'],
                'points': trajectory_data['coordinates']
            }
            
            # 发送JSON数据
            json_data = json.dumps(data_packet) + '\n'
            self.serial_port.write(json_data.encode())
            
            print(f"已发送 {trajectory_data['count']} 个轨迹点到云台")
            return True
            
        except Exception as e:
            print(f"发送数据失败: {e}")
            return False
    
    def get_gimbal_position(self):
        """获取云台当前位置"""
        if not self.serial_port:
            return None, None
        
        try:
            # 发送位置查询命令
            self.serial_port.write(b"get_position\n")
            
            # 读取响应
            response = self.serial_port.readline().decode().strip()
            if response:
                data = json.loads(response)
                return data.get('pan', 0), data.get('tilt', 0)
                
        except Exception as e:
            print(f"获取位置失败: {e}")
        
        return None, None

# 使用示例
def example_usage():
    processor = TrajectoryProcessor()
    
    # 模拟从视觉系统获取的轨迹点
    interpolated_points = np.array([
        [100, 200], [120, 210], [140, 220], [160, 230],
        [180, 240], [200, 250], [220, 260], [240, 270]
        # ... 更多点
    ])
    
    # 处理轨迹
    trajectory_data = processor.process_trajectory(interpolated_points)
    
    print(f"原始点数: {len(interpolated_points)}")
    print(f"简化后点数: {trajectory_data['count']}")
    print("简化后的坐标:")
    for i, coord in enumerate(trajectory_data['coordinates']):
        print(f"  点{i+1}: ({coord[0]:.3f}, {coord[1]:.3f})m")

if __name__ == "__main__":
    example_usage()
