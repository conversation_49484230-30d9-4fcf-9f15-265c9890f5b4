#include "gimbal_target_control.h"
#include "motor_mid.h"
#include "usart_mid.h"
#include "mydefine.h"
#include "math.h"

/**
 * @file gimbal_target_control.c
 * @brief 云台目标点控制系统
 * 
 * 基于目标点坐标计算云台角度，实现精确的目标跟踪控制
 */

// 云台目标控制配置参数
#define GIMBAL_DISTANCE_DEFAULT     0.9f    // 默认目标距离(米)
#define GIMBAL_HEIGHT_OFFSET        0.0f    // 云台高度偏移(米)
#define PI                          3.14159265359f
#define RAD_TO_DEG                  (180.0f / PI)
#define DEG_TO_RAD                  (PI / 180.0f)

// 全局变量
static float gimbal_distance = GIMBAL_DISTANCE_DEFAULT;
static float gimbal_height_offset = GIMBAL_HEIGHT_OFFSET;

/**
 * @brief 计算目标角度
 * @param x 目标点X坐标(米，相对于云台中心)
 * @param y 目标点Y坐标(米，相对于云台中心)
 * @param D 目标距离(米)
 * @param theta 输出水平角度(弧度)
 * @param phi 输出俯仰角度(弧度)
 */
void calculate_angles(float x, float y, float D, float* theta, float* phi) {
    // 计算水平角度 theta = atan2(y, D)
    *theta = atan2f(y, D);
    
    // 计算距离 L = sqrt(x² + y² + D²)
    float L = sqrtf(x * x + y * y + D * D);
    
    // 计算俯仰角度 phi = atan2(x, L)
    *phi = atan2f(x, L);
}

/**
 * @brief 设置云台目标距离
 * @param distance 目标距离(米)
 */
void gimbal_set_target_distance(float distance) {
    if(distance > 0.1f) {  // 最小距离限制
        gimbal_distance = distance;
    }
}

/**
 * @brief 设置云台高度偏移
 * @param height_offset 高度偏移(米)
 */
void gimbal_set_height_offset(float height_offset) {
    gimbal_height_offset = height_offset;
}

/**
 * @brief 获取当前目标距离
 * @return 当前设置的目标距离(米)
 */
float gimbal_get_target_distance(void) {
    return gimbal_distance;
}

/**
 * @brief 移动到目标点坐标
 * @param target_x 目标点X坐标(米，高度方向)
 * @param target_y 目标点Y坐标(米，水平方向)
 * @param speed 移动速度百分比(1-100)
 */
void gimbal_move_to_target(float target_x, float target_y, uint8_t speed) {
    float theta, phi;
    float theta_deg, phi_deg;
    
    // 应用高度偏移
    float adjusted_x = target_x - gimbal_height_offset;
    
    // 计算目标角度(弧度)
    calculate_angles(adjusted_x, target_y, gimbal_distance, &theta, &phi);
    
    // 转换为角度
    theta_deg = theta * RAD_TO_DEG;
    phi_deg = phi * RAD_TO_DEG;
    
    // 计算相对移动量
    float relative_pan = theta_deg - Motor_Cur_Pos[0];   // 水平相对角度
    float relative_tilt = phi_deg - Motor_Cur_Pos[1];    // 俯仰相对角度
    
    // 执行移动
    motor_move_angle(relative_pan, relative_tilt, speed, 50);
    
    // 调试输出
    uart_printf(UART_0_INST, "目标坐标: (%.2f, %.2f)m\r\n", target_x, target_y);
    uart_printf(UART_0_INST, "计算角度: 水平=%.2f°, 俯仰=%.2f°\r\n", theta_deg, phi_deg);
    uart_printf(UART_0_INST, "相对移动: 水平=%.2f°, 俯仰=%.2f°\r\n", relative_pan, relative_tilt);
}

/**
 * @brief 移动到绝对角度位置
 * @param pan_deg 水平角度(度)
 * @param tilt_deg 俯仰角度(度)
 * @param speed 移动速度百分比(1-100)
 */
void gimbal_move_to_absolute_angle(float pan_deg, float tilt_deg, uint8_t speed) {
    // 计算相对移动量
    float relative_pan = pan_deg - Motor_Cur_Pos[0];
    float relative_tilt = tilt_deg - Motor_Cur_Pos[1];
    
    // 执行移动
    motor_move_angle(relative_pan, relative_tilt, speed, 50);
    
    uart_printf(UART_0_INST, "移动到绝对角度: (%.2f°, %.2f°)\r\n", pan_deg, tilt_deg);
    uart_printf(UART_0_INST, "相对移动量: (%.2f°, %.2f°)\r\n", relative_pan, relative_tilt);
}

/**
 * @brief 像素坐标转目标点坐标
 * @param pixel_x 像素X坐标
 * @param pixel_y 像素Y坐标
 * @param target_x 输出目标点X坐标(米)
 * @param target_y 输出目标点Y坐标(米)
 */
void pixel_to_target_coordinate(int pixel_x, int pixel_y, float* target_x, float* target_y) {
    // 先转换为角度
    float angle_x = pixel_to_angle_x(pixel_x);
    float angle_y = pixel_to_angle_y(pixel_y);
    
    // 角度转弧度
    float theta_rad = angle_x * DEG_TO_RAD;  // 水平角度
    float phi_rad = angle_y * DEG_TO_RAD;    // 俯仰角度
    
    // 根据角度和距离计算目标坐标
    // 反向计算：从角度推导坐标
    *target_y = gimbal_distance * tanf(theta_rad);  // 水平坐标
    *target_x = gimbal_distance * tanf(phi_rad);    // 高度坐标
}

/**
 * @brief 移动到像素位置(使用目标坐标方式)
 * @param pixel_x 像素X坐标
 * @param pixel_y 像素Y坐标
 * @param speed 移动速度百分比(1-100)
 */
void gimbal_move_to_pixel_target(int pixel_x, int pixel_y, uint8_t speed) {
    float target_x, target_y;
    
    // 像素转目标坐标
    pixel_to_target_coordinate(pixel_x, pixel_y, &target_x, &target_y);
    
    // 移动到目标坐标
    gimbal_move_to_target(target_x, target_y, speed);
    
    uart_printf(UART_0_INST, "像素(%d,%d) -> 目标坐标(%.3f,%.3f)m\r\n", 
                pixel_x, pixel_y, target_x, target_y);
}

/**
 * @brief 目标控制系统测试
 */
void gimbal_target_control_test(void) {
    uart_printf(UART_0_INST, "=== 云台目标控制测试 ===\r\n");
    uart_printf(UART_0_INST, "目标距离: %.2fm\r\n", gimbal_distance);
    uart_printf(UART_0_INST, "高度偏移: %.2fm\r\n", gimbal_height_offset);
    
    // 测试1：移动到中心点
    uart_printf(UART_0_INST, "\n测试1：移动到中心点(0,0)\r\n");
    gimbal_move_to_target(0.0f, 0.0f, 30);
    delay_ms(3000);
    
    // 测试2：移动到右上角
    uart_printf(UART_0_INST, "\n测试2：移动到右上角(0.2,0.3)\r\n");
    gimbal_move_to_target(0.2f, 0.3f, 30);
    delay_ms(3000);
    
    // 测试3：使用像素坐标
    uart_printf(UART_0_INST, "\n测试3：使用像素坐标(160,120)\r\n");
    gimbal_move_to_pixel_target(160, 120, 30);
    delay_ms(3000);
    
    // 测试4：回到中心
    uart_printf(UART_0_INST, "\n测试4：回到中心\r\n");
    gimbal_move_to_target(0.0f, 0.0f, 30);
    
    uart_printf(UART_0_INST, "\n=== 测试完成 ===\r\n");
}

// 目标控制状态机枚举
typedef enum {
    TARGET_CONTROL_IDLE = 0,
    TARGET_CONTROL_MOVING,
    TARGET_CONTROL_REACHED,
    TARGET_CONTROL_TIMEOUT
} target_control_state_t;

// 目标控制状态机结构体
typedef struct {
    target_control_state_t state;
    float target_pan_deg;
    float target_tilt_deg;
    float tolerance_pan;
    float tolerance_tilt;
    uint32_t start_time;
    uint32_t timeout_ms;
    uint8_t completed;
} target_control_t;

// 全局状态机实例
static target_control_t target_control = {0};

/**
 * @brief 检查云台是否到达目标位置
 * @param target_pan 目标水平角度(度)
 * @param target_tilt 目标俯仰角度(度)
 * @param tolerance_pan 水平容差(度)
 * @param tolerance_tilt 俯仰容差(度)
 * @return 1-已到达，0-未到达
 */
uint8_t gimbal_check_target_reached(float target_pan, float target_tilt, float tolerance_pan, float tolerance_tilt) {
    return check_motor_position_in_range(target_pan, target_tilt, tolerance_pan, tolerance_tilt);
}

/**
 * @brief 初始化目标控制状态机
 * @param target_pan 目标水平角度(度)
 * @param target_tilt 目标俯仰角度(度)
 * @param tolerance_pan 水平容差(度)
 * @param tolerance_tilt 俯仰容差(度)
 * @param timeout_ms 超时时间(毫秒)
 */
void gimbal_target_control_init(float target_pan, float target_tilt, float tolerance_pan, float tolerance_tilt, uint32_t timeout_ms) {
    target_control.state = TARGET_CONTROL_MOVING;
    target_control.target_pan_deg = target_pan;
    target_control.target_tilt_deg = target_tilt;
    target_control.tolerance_pan = tolerance_pan;
    target_control.tolerance_tilt = tolerance_tilt;
    target_control.start_time = HAL_GetTick(); // 或者使用您的时间函数
    target_control.timeout_ms = timeout_ms;
    target_control.completed = 0;
}

/**
 * @brief 目标控制状态机任务
 * @return 1-任务完成，0-任务进行中
 */
uint8_t gimbal_target_control_task(void) {
    uint32_t current_time = HAL_GetTick(); // 或者使用您的时间函数

    switch(target_control.state) {
        case TARGET_CONTROL_IDLE:
            // 空闲状态，等待新任务
            break;

        case TARGET_CONTROL_MOVING:
            // 检查是否到达目标位置
            if(gimbal_check_target_reached(target_control.target_pan_deg, target_control.target_tilt_deg,
                                         target_control.tolerance_pan, target_control.tolerance_tilt)) {
                target_control.state = TARGET_CONTROL_REACHED;
                target_control.completed = 1;
                uart_printf(UART_0_INST, "✓ 目标到达: (%.2f°,%.2f°)\r\n",
                           Motor_Cur_Pos[0], Motor_Cur_Pos[1]);
                return 1; // 任务完成
            }

            // 检查超时
            if((current_time - target_control.start_time) > target_control.timeout_ms) {
                target_control.state = TARGET_CONTROL_TIMEOUT;
                uart_printf(UART_0_INST, "⚠ 目标控制超时\r\n");
                return 1; // 任务完成(超时)
            }
            break;

        case TARGET_CONTROL_REACHED:
            // 已到达目标，保持状态
            target_control.state = TARGET_CONTROL_IDLE;
            break;

        case TARGET_CONTROL_TIMEOUT:
            // 超时状态，重置为空闲
            target_control.state = TARGET_CONTROL_IDLE;
            break;
    }

    return 0; // 任务进行中
}

/**
 * @brief 带状态机的目标移动函数
 * @param target_x 目标点X坐标(米)
 * @param target_y 目标点Y坐标(米)
 * @param speed 移动速度百分比(1-100)
 * @param tolerance_pan 水平容差(度)
 * @param tolerance_tilt 俯仰容差(度)
 * @param timeout_ms 超时时间(毫秒)
 */
void gimbal_move_to_target_with_feedback(float target_x, float target_y, uint8_t speed,
                                       float tolerance_pan, float tolerance_tilt, uint32_t timeout_ms) {
    float theta, phi;
    float theta_deg, phi_deg;

    // 应用高度偏移
    float adjusted_x = target_x - gimbal_height_offset;

    // 计算目标角度(弧度)
    calculate_angles(adjusted_x, target_y, gimbal_distance, &theta, &phi);

    // 转换为角度
    theta_deg = theta * RAD_TO_DEG;
    phi_deg = phi * RAD_TO_DEG;

    // 计算相对移动量
    float relative_pan = theta_deg - Motor_Cur_Pos[0];
    float relative_tilt = phi_deg - Motor_Cur_Pos[1];

    // 初始化状态机
    gimbal_target_control_init(theta_deg, phi_deg, tolerance_pan, tolerance_tilt, timeout_ms);

    // 执行移动
    motor_move_angle(relative_pan, relative_tilt, speed, 50);

    uart_printf(UART_0_INST, "开始移动到目标: (%.2f,%.2f)m -> (%.2f°,%.2f°)\r\n",
                target_x, target_y, theta_deg, phi_deg);
}

/**
 * @brief 获取目标控制状态
 * @return 当前状态
 */
target_control_state_t gimbal_get_target_control_state(void) {
    return target_control.state;
}

/**
 * @brief 目标控制是否完成
 * @return 1-完成，0-进行中
 */
uint8_t gimbal_target_control_completed(void) {
    return target_control.completed;
}

/**
 * @brief 计算角度示例(类似您提供的代码)
 */
void gimbal_calculate_example(void) {
    float D = 0.9f;   // 云台到目标平面的距离(单位：米)
    float x = 0.5f;   // 目标点X坐标(单位：米)
    float y = 0.3f;   // 目标点Y坐标(单位：米)

    float theta, phi;
    calculate_angles(x, y, D, &theta, &phi);

    // 弧度转角度
    float theta_deg = theta * RAD_TO_DEG;
    float phi_deg = phi * RAD_TO_DEG;

    uart_printf(UART_0_INST, "计算结果:\r\n");
    uart_printf(UART_0_INST, "水平角 θ = %.2f rad (%.2f deg)\r\n", theta, theta_deg);
    uart_printf(UART_0_INST, "俯仰角 φ = %.2f rad (%.2f deg)\r\n", phi, phi_deg);
}
