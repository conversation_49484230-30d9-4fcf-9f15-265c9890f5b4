#include "gimbal_target_control.h"
#include "motor_mid.h"
#include "usart_mid.h"
#include "mydefine.h"
#include "math.h"

/**
 * @file gimbal_target_control.c
 * @brief 云台目标点控制系统
 * 
 * 基于目标点坐标计算云台角度，实现精确的目标跟踪控制
 */

// 云台目标控制配置参数
#define GIMBAL_DISTANCE_DEFAULT     0.9f    // 默认目标距离(米)
#define GIMBAL_HEIGHT_OFFSET        0.0f    // 云台高度偏移(米)
#define PI                          3.14159265359f
#define RAD_TO_DEG                  (180.0f / PI)
#define DEG_TO_RAD                  (PI / 180.0f)

// 全局变量
static float gimbal_distance = GIMBAL_DISTANCE_DEFAULT;
static float gimbal_height_offset = GIMBAL_HEIGHT_OFFSET;

/**
 * @brief 计算目标角度
 * @param x 目标点X坐标(米，相对于云台中心)
 * @param y 目标点Y坐标(米，相对于云台中心)
 * @param D 目标距离(米)
 * @param theta 输出水平角度(弧度)
 * @param phi 输出俯仰角度(弧度)
 */
void calculate_angles(float x, float y, float D, float* theta, float* phi) {
    // 计算水平角度 theta = atan2(y, D)
    *theta = atan2f(y, D);
    
    // 计算距离 L = sqrt(x² + y² + D²)
    float L = sqrtf(x * x + y * y + D * D);
    
    // 计算俯仰角度 phi = atan2(x, L)
    *phi = atan2f(x, L);
}

/**
 * @brief 设置云台目标距离
 * @param distance 目标距离(米)
 */
void gimbal_set_target_distance(float distance) {
    if(distance > 0.1f) {  // 最小距离限制
        gimbal_distance = distance;
    }
}

/**
 * @brief 设置云台高度偏移
 * @param height_offset 高度偏移(米)
 */
void gimbal_set_height_offset(float height_offset) {
    gimbal_height_offset = height_offset;
}

/**
 * @brief 获取当前目标距离
 * @return 当前设置的目标距离(米)
 */
float gimbal_get_target_distance(void) {
    return gimbal_distance;
}

/**
 * @brief 移动到目标点坐标
 * @param target_x 目标点X坐标(米，高度方向)
 * @param target_y 目标点Y坐标(米，水平方向)
 * @param speed 移动速度百分比(1-100)
 */
void gimbal_move_to_target(float target_x, float target_y, uint8_t speed) {
    float theta, phi;
    float theta_deg, phi_deg;
    
    // 应用高度偏移
    float adjusted_x = target_x - gimbal_height_offset;
    
    // 计算目标角度(弧度)
    calculate_angles(adjusted_x, target_y, gimbal_distance, &theta, &phi);
    
    // 转换为角度
    theta_deg = theta * RAD_TO_DEG;
    phi_deg = phi * RAD_TO_DEG;
    
    // 计算相对移动量
    float relative_pan = theta_deg - Motor_Cur_Pos[0];   // 水平相对角度
    float relative_tilt = phi_deg - Motor_Cur_Pos[1];    // 俯仰相对角度
    
    // 执行移动
    motor_move_angle(relative_pan, relative_tilt, speed, 50);
    
    // 调试输出
    uart_printf(UART_0_INST, "目标坐标: (%.2f, %.2f)m\r\n", target_x, target_y);
    uart_printf(UART_0_INST, "计算角度: 水平=%.2f°, 俯仰=%.2f°\r\n", theta_deg, phi_deg);
    uart_printf(UART_0_INST, "相对移动: 水平=%.2f°, 俯仰=%.2f°\r\n", relative_pan, relative_tilt);
}

/**
 * @brief 移动到绝对角度位置
 * @param pan_deg 水平角度(度)
 * @param tilt_deg 俯仰角度(度)
 * @param speed 移动速度百分比(1-100)
 */
void gimbal_move_to_absolute_angle(float pan_deg, float tilt_deg, uint8_t speed) {
    // 计算相对移动量
    float relative_pan = pan_deg - Motor_Cur_Pos[0];
    float relative_tilt = tilt_deg - Motor_Cur_Pos[1];
    
    // 执行移动
    motor_move_angle(relative_pan, relative_tilt, speed, 50);
    
    uart_printf(UART_0_INST, "移动到绝对角度: (%.2f°, %.2f°)\r\n", pan_deg, tilt_deg);
    uart_printf(UART_0_INST, "相对移动量: (%.2f°, %.2f°)\r\n", relative_pan, relative_tilt);
}

/**
 * @brief 像素坐标转目标点坐标
 * @param pixel_x 像素X坐标
 * @param pixel_y 像素Y坐标
 * @param target_x 输出目标点X坐标(米)
 * @param target_y 输出目标点Y坐标(米)
 */
void pixel_to_target_coordinate(int pixel_x, int pixel_y, float* target_x, float* target_y) {
    // 先转换为角度
    float angle_x = pixel_to_angle_x(pixel_x);
    float angle_y = pixel_to_angle_y(pixel_y);
    
    // 角度转弧度
    float theta_rad = angle_x * DEG_TO_RAD;  // 水平角度
    float phi_rad = angle_y * DEG_TO_RAD;    // 俯仰角度
    
    // 根据角度和距离计算目标坐标
    // 反向计算：从角度推导坐标
    *target_y = gimbal_distance * tanf(theta_rad);  // 水平坐标
    *target_x = gimbal_distance * tanf(phi_rad);    // 高度坐标
}

/**
 * @brief 移动到像素位置(使用目标坐标方式)
 * @param pixel_x 像素X坐标
 * @param pixel_y 像素Y坐标
 * @param speed 移动速度百分比(1-100)
 */
void gimbal_move_to_pixel_target(int pixel_x, int pixel_y, uint8_t speed) {
    float target_x, target_y;
    
    // 像素转目标坐标
    pixel_to_target_coordinate(pixel_x, pixel_y, &target_x, &target_y);
    
    // 移动到目标坐标
    gimbal_move_to_target(target_x, target_y, speed);
    
    uart_printf(UART_0_INST, "像素(%d,%d) -> 目标坐标(%.3f,%.3f)m\r\n", 
                pixel_x, pixel_y, target_x, target_y);
}

/**
 * @brief 目标控制系统测试
 */
void gimbal_target_control_test(void) {
    uart_printf(UART_0_INST, "=== 云台目标控制测试 ===\r\n");
    uart_printf(UART_0_INST, "目标距离: %.2fm\r\n", gimbal_distance);
    uart_printf(UART_0_INST, "高度偏移: %.2fm\r\n", gimbal_height_offset);
    
    // 测试1：移动到中心点
    uart_printf(UART_0_INST, "\n测试1：移动到中心点(0,0)\r\n");
    gimbal_move_to_target(0.0f, 0.0f, 30);
    delay_ms(3000);
    
    // 测试2：移动到右上角
    uart_printf(UART_0_INST, "\n测试2：移动到右上角(0.2,0.3)\r\n");
    gimbal_move_to_target(0.2f, 0.3f, 30);
    delay_ms(3000);
    
    // 测试3：使用像素坐标
    uart_printf(UART_0_INST, "\n测试3：使用像素坐标(160,120)\r\n");
    gimbal_move_to_pixel_target(160, 120, 30);
    delay_ms(3000);
    
    // 测试4：回到中心
    uart_printf(UART_0_INST, "\n测试4：回到中心\r\n");
    gimbal_move_to_target(0.0f, 0.0f, 30);
    
    uart_printf(UART_0_INST, "\n=== 测试完成 ===\r\n");
}

/**
 * @brief 反向解算：从角度计算对应的坐标
 * @param theta 水平角度(弧度)
 * @param phi 俯仰角度(弧度)
 * @param D 目标距离(米)
 * @param x 输出X坐标(米，高度方向)
 * @param y 输出Y坐标(米，水平方向)
 */
void calculate_coordinates_from_angles(float theta, float phi, float D, float* x, float* y) {
    // 反向解算公式：
    // 从 theta = atan2(y, D) 得到：y = D * tan(theta)
    *y = D * tanf(theta);

    // 从 phi = atan2(x, L) 和 L = sqrt(x² + y² + D²) 得到：
    // x = L * tan(phi)
    // 但 L = sqrt(x² + y² + D²)，需要求解
    // 简化：x ≈ D * tan(phi) (当角度较小时的近似)
    // 精确解法：x = D * tan(phi) / cos(theta)
    float cos_theta = cosf(theta);
    if(fabsf(cos_theta) > 0.001f) {  // 避免除零
        *x = D * tanf(phi) / cos_theta;
    } else {
        *x = D * tanf(phi);  // 退化为近似解
    }
}

/**
 * @brief 获取当前云台指向的坐标
 * @param current_x 输出当前X坐标(米)
 * @param current_y 输出当前Y坐标(米)
 */
void gimbal_get_current_coordinates(float* current_x, float* current_y) {
    // 获取当前角度并转换为弧度
    float current_theta = Motor_Cur_Pos[0] * DEG_TO_RAD;  // 水平角度
    float current_phi = Motor_Cur_Pos[1] * DEG_TO_RAD;    // 俯仰角度

    // 反向解算得到坐标
    calculate_coordinates_from_angles(current_theta, current_phi, gimbal_distance, current_x, current_y);

    // 应用高度偏移
    *current_x += gimbal_height_offset;
}

/**
 * @brief 检查云台是否到达目标坐标位置
 * @param target_x 目标X坐标(米)
 * @param target_y 目标Y坐标(米)
 * @param tolerance_x X轴坐标容差(米)
 * @param tolerance_y Y轴坐标容差(米)
 * @return 1-已到达，0-未到达
 */
uint8_t gimbal_check_coordinate_reached(float target_x, float target_y, float tolerance_x, float tolerance_y) {
    float current_x, current_y;

    // 获取当前云台指向的坐标
    gimbal_get_current_coordinates(&current_x, &current_y);

    // 计算坐标差值的绝对值
    float diff_x = fabsf(current_x - target_x);
    float diff_y = fabsf(current_y - target_y);

    // 检查是否都在允许范围内
    if(diff_x <= tolerance_x && diff_y <= tolerance_y) {
        return 1; // 在允许范围内
    } else {
        return 0; // 超出允许范围
    }
}

/**
 * @brief 检查云台是否到达目标角度位置
 * @param target_pan 目标水平角度(度)
 * @param target_tilt 目标俯仰角度(度)
 * @param tolerance_pan 水平容差(度)
 * @param tolerance_tilt 俯仰容差(度)
 * @return 1-已到达，0-未到达
 */
uint8_t gimbal_check_target_reached(float target_pan, float target_tilt, float tolerance_pan, float tolerance_tilt) {
    // 计算X轴差值的绝对值
    float diff_x = fabsf(Motor_Cur_Pos[0] - target_pan);

    // 计算Y轴差值的绝对值
    float diff_y = fabsf(Motor_Cur_Pos[1] - target_tilt);

    // 检查是否都在允许范围内
    if(diff_x <= tolerance_pan && diff_y <= tolerance_tilt) {
        return 1; // 在允许范围内
    } else {
        return 0; // 超出允许范围
    }
}

/**
 * @brief 计算角度示例(类似您提供的代码)
 */
void gimbal_calculate_example(void) {
    float D = 0.9f;   // 云台到目标平面的距离(单位：米)
    float x = 0.5f;   // 目标点X坐标(单位：米)
    float y = 0.3f;   // 目标点Y坐标(单位：米)

    float theta, phi;
    calculate_angles(x, y, D, &theta, &phi);

    // 弧度转角度
    float theta_deg = theta * RAD_TO_DEG;
    float phi_deg = phi * RAD_TO_DEG;

    uart_printf(UART_0_INST, "计算结果:\r\n");
    uart_printf(UART_0_INST, "水平角 θ = %.2f rad (%.2f deg)\r\n", theta, theta_deg);
    uart_printf(UART_0_INST, "俯仰角 φ = %.2f rad (%.2f deg)\r\n", phi, phi_deg);
}
